/**
 * 瞬光捕手 - 用户管理界面
 * 提供用户创建、切换、管理的图形界面
 */

class UserManagementUI {
    constructor() {
        this.initialized = false;
        this.userManager = null;
        this.userSwitchManager = null;
        this.currentModal = null;
        
        console.log('🎨 用户管理界面已创建');
    }

    /**
     * 初始化用户管理界面
     */
    async init() {
        try {
            // 获取依赖服务 - 优先从适配器获取最新实例
            this.userManager = null;
            this.userSwitchManager = window.userSwitchManager;

            // 使用统一的方法获取用户管理器引用
            this.refreshUserManagerReference();

            if (!this.userManager) {
                console.warn('⚠️ 用户管理器未找到，尝试备用方法');
                this.userManager = window.userManager;
            }

            if (!this.userManager) {
                throw new Error('用户管理器未找到，请检查用户系统适配器是否正确初始化');
            }

            // 智能等待用户管理器完成初始化
            if (!this.userManager.initialized) {
                console.log('⏳ 等待用户管理器完成初始化...');

                let retryCount = 0;
                const maxRetries = 30; // 减少到3秒等待

                while (!this.userManager.initialized && retryCount < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    retryCount++;
                }

                if (!this.userManager.initialized) {
                    console.warn('⚠️ 用户管理器初始化超时，但继续初始化界面');

                    // 尝试强制触发初始化
                    if (typeof this.userManager.init === 'function') {
                        console.log('🔄 尝试强制重新初始化用户管理器...');
                        try {
                            await this.userManager.init();
                            console.log('✅ 强制初始化成功');
                        } catch (error) {
                            console.warn('⚠️ 强制初始化失败，但继续:', error.message);
                        }
                    }
                }
            }

            console.log('✅ 用户管理器已获取:', this.userManager.constructor.name);

            // 创建界面元素
            this.createUserManagementModal();
            this.createUserInfoDisplay();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 更新界面显示
            this.updateUserDisplay();
            
            this.initialized = true;
            console.log('✅ 用户管理界面初始化完成');

            // 延迟更新用户显示，确保DOM元素已准备好
            setTimeout(() => {
                this.updateUserDisplay();
                console.log('🔄 初始化后更新用户显示');

                // 再次延迟更新，确保所有组件都已初始化
                setTimeout(() => {
                    this.updateUserDisplay();
                    console.log('🔄 二次延迟更新用户显示');
                }, 200);
            }, 100);

        } catch (error) {
            console.error('❌ 用户管理界面初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建用户管理模态框
     */
    createUserManagementModal() {
        const modalHtml = `
            <div id="user-management-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>👤 用户管理</h2>
                        <button class="modal-close" onclick="userManagementUI.hideModal()">&times;</button>
                    </div>
                    
                    <div class="modal-body">
                        <!-- 当前用户信息 -->
                        <div class="current-user-section">
                            <h3>当前用户</h3>
                            <div id="current-user-info" class="user-info-card">
                                <div class="user-avatar">👤</div>
                                <div class="user-details">
                                    <div class="user-name">未知用户</div>
                                    <div class="user-identifier">unknown</div>
                                    <div class="user-stats">
                                        <span class="stat-item">创建时间: --</span>
                                        <span class="stat-item">最后活跃: --</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户列表 -->
                        <div class="user-list-section">
                            <h3>所有用户</h3>
                            <div id="user-list" class="user-list">
                                <!-- 用户列表将在这里动态生成 -->
                            </div>
                        </div>

                        <!-- 创建新用户 -->
                        <div class="create-user-section">
                            <h3>创建新用户</h3>
                            <div class="create-user-form">
                                <div class="form-group">
                                    <label for="new-user-identifier">用户标识符:</label>
                                    <input type="text" id="new-user-identifier" 
                                           placeholder="输入唯一的用户标识符" 
                                           maxlength="32" 
                                           pattern="[a-zA-Z0-9_-]+"
                                           title="只允许字母、数字、下划线和连字符">
                                    <small>用于系统内部识别，创建后不可修改</small>
                                </div>
                                <div class="form-group">
                                    <label for="new-user-display-name">显示名称:</label>
                                    <input type="text" id="new-user-display-name" 
                                           placeholder="输入显示名称" 
                                           maxlength="50">
                                    <small>其他用户看到的名称，可以随时修改</small>
                                </div>
                                <div class="form-actions">
                                    <button id="create-user-btn" class="primary-btn" onclick="userManagementUI.handleCreateUser()">
                                        创建用户
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 用户操作 -->
                        <div class="user-actions-section">
                            <h3>用户操作</h3>
                            <div class="action-buttons">
                                <button class="action-btn" onclick="userManagementUI.exportCurrentUser()">
                                    📤 导出当前用户数据
                                </button>
                                <button class="action-btn" onclick="userManagementUI.showImportDialog()">
                                    📥 导入用户数据
                                </button>
                                <button class="action-btn" onclick="userManagementUI.showDataStats()">
                                    📊 查看数据统计
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        console.log('✅ 用户管理模态框HTML已添加到页面');
    }

    /**
     * 创建用户切换按钮
     */
    createUserSwitchButton() {
        // 查找合适的位置插入用户切换按钮
        const gameHeader = document.querySelector('.game-header') || document.querySelector('header');
        
        if (gameHeader) {
            const userSwitchHtml = `
                <div id="user-switch-container" class="user-switch-container">
                    <button id="user-switch-btn" class="user-switch-btn" onclick="userManagementUI.showModal()">
                        <span class="user-icon">👤</span>
                        <span id="current-user-display" class="user-display">游客</span>
                        <span class="switch-icon">⚙️</span>
                    </button>
                </div>
            `;
            
            gameHeader.insertAdjacentHTML('beforeend', userSwitchHtml);
        }
    }

    /**
     * 创建用户信息显示
     */
    createUserInfoDisplay() {
        // 在游戏界面中添加用户信息显示区域
        const gameUI = document.querySelector('.game-ui');

        if (gameUI) {
            // 检查是否已存在用户信息显示
            const existingDisplay = document.getElementById('user-info-display');
            if (existingDisplay) {
                existingDisplay.remove();
            }

            const userInfoHtml = `
                <div id="user-info-display" class="user-info-display">
                    <div class="user-info-compact">
                        <span class="user-icon">👤</span>
                        <span class="user-name-display" id="user-name-display">游客</span>
                        <span class="user-type-badge" id="user-type-badge">guest</span>
                    </div>
                </div>
            `;

            gameUI.insertAdjacentHTML('afterbegin', userInfoHtml);
        }
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听用户管理器事件（仅监听用户删除事件，其他事件由全局监听器处理）
        if (this.userManager) {
            // 注意：用户切换和创建事件已由全局监听器处理，避免重复监听
            console.log('📋 跳过用户切换和创建事件监听（由全局监听器处理）');

            this.userManager.addEventListener('userDeleted', (eventData) => {
                console.log('🗑️ 用户删除事件触发');
                this.updateUserList();
                this.showSuccessMessage(`用户删除成功: ${eventData.identifier}`);
            });
        }

        // 监听用户切换事件
        window.addEventListener('userSwitch:completed', (event) => {
            this.showSuccessMessage(`切换到用户: ${event.detail.currentUser.displayName}`);
        });

        window.addEventListener('userSwitch:failed', (event) => {
            this.showErrorMessage(`用户切换失败: ${event.detail.error}`);
        });

        // 监听键盘事件
        document.addEventListener('keydown', (event) => {
            // Ctrl+U 快速打开用户管理
            if (event.ctrlKey && event.key === 'u') {
                event.preventDefault();
                this.showModal();
            }
            
            // ESC 关闭模态框
            if (event.key === 'Escape' && this.currentModal) {
                this.hideModal();
            }
        });
    }

    /**
     * 显示用户管理模态框
     */
    showModal() {
        console.log('🔍 尝试显示用户管理模态框...');

        const modal = document.getElementById('user-management-modal');
        if (modal) {
            console.log('✅ 找到用户管理模态框，正在显示...');
            modal.classList.remove('hidden');
            this.currentModal = modal;
            this.updateCurrentUserInfo();
            this.updateUserList();
        } else {
            console.error('❌ 用户管理模态框未找到，尝试重新创建...');
            this.createUserManagementModal();
            // 重试显示
            setTimeout(() => {
                const retryModal = document.getElementById('user-management-modal');
                if (retryModal) {
                    retryModal.classList.remove('hidden');
                    this.currentModal = retryModal;
                    this.updateCurrentUserInfo();
                    this.updateUserList();
                }
            }, 100);
        }
    }

    /**
     * 隐藏用户管理模态框
     */
    hideModal() {
        const modal = document.getElementById('user-management-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.currentModal = null;
        }
    }

    /**
     * 刷新用户管理器引用
     */
    refreshUserManagerReference() {
        // 始终从全局对象重新获取用户管理器引用，确保获取最新实例
        if (window.userSystemAdapter && window.userSystemAdapter.getUserManager) {
            try {
                const newUserManager = window.userSystemAdapter.getUserManager();

                // 比较实例是否相同
                if (this.userManager !== newUserManager) {
                    console.log('🔄 检测到用户管理器实例变化，更新引用');
                    console.log(`📋 旧实例: ${this.userManager ? this.userManager.constructor.name : 'null'}`);
                    console.log(`📋 新实例: ${newUserManager ? newUserManager.constructor.name : 'null'}`);

                    this.userManager = newUserManager;
                    console.log('✅ 用户管理器引用已更新');
                } else {
                    console.log('📋 用户管理器实例未变化，保持当前引用');
                }

                // 验证实例的当前用户状态
                if (this.userManager && typeof this.userManager.getCurrentUser === 'function') {
                    const currentUser = this.userManager.getCurrentUser();
                    console.log(`📋 当前实例的用户状态: ${currentUser ? currentUser.displayName : 'null'} (${currentUser ? currentUser.identifier : 'N/A'})`);
                }

            } catch (error) {
                console.warn('⚠️ 刷新用户管理器引用失败:', error);
            }
        } else {
            console.warn('⚠️ 无法获取用户系统适配器或其getUserManager方法');
        }
    }

    /**
     * 更新用户显示
     */
    updateUserDisplay() {
        // 详细的状态检查和日志
        console.log('🔍 开始更新用户显示...');

        // 始终刷新用户管理器引用，确保获取最新实例
        this.refreshUserManagerReference();

        console.log(`📋 用户管理器状态: ${this.userManager ? '已初始化' : '未初始化'}`);

        if (this.userManager) {
            console.log(`📋 用户管理器类型: ${this.userManager.constructor.name}`);
            console.log(`📋 用户管理器当前用户方法: ${typeof this.userManager.getCurrentUser}`);

            // 验证实例是否与全局实例相同
            if (window.userSystemAdapter && window.userSystemAdapter.getUserManager) {
                const globalUserManager = window.userSystemAdapter.getUserManager();
                const isSameInstance = this.userManager === globalUserManager;
                console.log(`📋 实例一致性检查: ${isSameInstance ? '✅ 相同' : '❌ 不同'}`);

                if (!isSameInstance) {
                    console.warn('⚠️ 检测到实例不一致，强制更新引用');
                    this.userManager = globalUserManager;
                }
            }
        }

        const currentUser = this.userManager ? this.userManager.getCurrentUser() : null;
        console.log(`📋 获取到的当前用户对象:`, currentUser);

        const displayName = currentUser ? currentUser.displayName : '游客';
        const userType = currentUser ? currentUser.type : 'guest';
        const identifier = currentUser ? currentUser.identifier : 'guest';

        console.log(`🔄 更新用户显示: ${displayName} (${userType}) [${identifier}]`);

        // 更新切换按钮显示
        const userDisplay = document.getElementById('current-user-display');
        if (userDisplay) {
            userDisplay.textContent = displayName;
            console.log(`✅ 已更新切换按钮显示: ${displayName}`);
        } else {
            console.log(`⚠️ 未找到切换按钮元素: current-user-display`);
        }

        // 更新游戏界面中的用户信息显示
        const userNameDisplay = document.getElementById('user-name-display');
        const userTypeBadge = document.getElementById('user-type-badge');

        if (userNameDisplay) {
            userNameDisplay.textContent = displayName;
            console.log(`✅ 已更新游戏界面用户名显示: ${displayName}`);
        } else {
            console.log(`⚠️ 未找到游戏界面用户名元素: user-name-display`);
        }

        if (userTypeBadge) {
            userTypeBadge.textContent = userType;
            userTypeBadge.className = `user-type-badge ${userType}`;
            console.log(`✅ 已更新用户类型标识: ${userType}`);
        } else {
            console.log(`⚠️ 未找到用户类型标识元素: user-type-badge`);
        }

        // 更新主菜单中的用户信息显示
        const currentUserName = document.getElementById('current-user-name');
        if (currentUserName) {
            currentUserName.textContent = displayName;
            console.log(`✅ 已更新主菜单用户名显示: ${displayName}`);
        } else {
            console.log(`⚠️ 未找到主菜单用户名元素: current-user-name`);
        }

        // 同时更新旧的玩家管理系统显示（保持兼容性）
        const currentPlayerName = document.getElementById('current-player-name');
        if (currentPlayerName) {
            currentPlayerName.textContent = displayName;
        }
    }

    /**
     * 更新当前用户信息
     */
    updateCurrentUserInfo() {
        const currentUser = this.userManager.getCurrentUser();
        const userInfoElement = document.getElementById('current-user-info');

        if (userInfoElement && currentUser) {
            const userNameElement = userInfoElement.querySelector('.user-name');
            const userIdentifierElement = userInfoElement.querySelector('.user-identifier');
            const userStatsElement = userInfoElement.querySelector('.user-stats');

            if (userNameElement) {
                userNameElement.textContent = currentUser.displayName;
            }

            if (userIdentifierElement) {
                userIdentifierElement.textContent = currentUser.identifier;
            }

            if (userStatsElement) {
                const createdDate = new Date(currentUser.createdAt).toLocaleDateString();
                const lastActiveDate = new Date(currentUser.lastActiveAt).toLocaleDateString();

                userStatsElement.innerHTML = `
                    <span class="stat-item">创建时间: ${createdDate}</span>
                    <span class="stat-item">最后活跃: ${lastActiveDate}</span>
                `;
            }
        }
    }

    /**
     * 更新用户列表
     */
    updateUserList() {
        const userListElement = document.getElementById('user-list');
        if (!userListElement) return;

        const users = this.userManager.getAllUsers();
        const currentUser = this.userManager.getCurrentUser();

        userListElement.innerHTML = '';

        users.forEach(user => {
            const userItem = this.createUserListItem(user, currentUser);
            userListElement.appendChild(userItem);
        });
    }

    /**
     * 创建用户列表项
     * @param {Object} user - 用户对象
     * @param {Object} currentUser - 当前用户对象
     * @returns {HTMLElement} 用户列表项元素
     */
    createUserListItem(user, currentUser) {
        const isCurrentUser = currentUser && currentUser.identifier === user.identifier;

        const userItem = document.createElement('div');
        userItem.className = `user-list-item ${isCurrentUser ? 'current' : ''}`;

        userItem.innerHTML = `
            <div class="user-item-info">
                <div class="user-item-avatar">👤</div>
                <div class="user-item-details">
                    <div class="user-item-name">${user.displayName}</div>
                    <div class="user-item-identifier">${user.identifier}</div>
                    <div class="user-item-meta">
                        <span class="user-type-badge ${user.type}">${user.type}</span>
                        <span class="user-last-active">最后活跃: ${new Date(user.lastActiveAt).toLocaleDateString()}</span>
                    </div>
                </div>
            </div>
            <div class="user-item-actions">
                ${!isCurrentUser ? `
                    <button class="action-btn switch-btn" onclick="userManagementUI.switchToUser('${user.identifier}')">
                        切换
                    </button>
                ` : '<span class="current-badge">当前用户</span>'}

                ${user.type !== 'guest' ? `
                    <button class="action-btn edit-btn" onclick="userManagementUI.editUser('${user.identifier}')">
                        编辑
                    </button>
                ` : ''}

                ${user.type !== 'guest' && !isCurrentUser ? `
                    <button class="action-btn delete-btn" onclick="userManagementUI.deleteUser('${user.identifier}')">
                        删除
                    </button>
                ` : ''}
            </div>
        `;

        return userItem;
    }

    /**
     * 智能检查并等待初始化完成
     */
    async checkAndWaitForInitialization() {
        console.log('🔍 检查系统初始化状态...');

        // 检查用户管理器是否存在
        if (!this.userManager) {
            return {
                success: false,
                message: '用户管理器未找到，请刷新页面重试'
            };
        }

        // 如果用户管理器未初始化，等待一段时间
        if (!this.userManager.initialized) {
            console.log('⏳ 用户管理器正在初始化，等待完成...');

            let retryCount = 0;
            const maxRetries = 20; // 减少到2秒等待

            while (!this.userManager.initialized && retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retryCount++;

                // 每秒输出一次等待日志
                if (retryCount % 10 === 0) {
                    console.log(`⏳ 等待用户管理器初始化... (${retryCount / 10}秒)`);
                }
            }

            if (!this.userManager.initialized) {
                // 尝试强制初始化
                console.log('🔄 尝试强制初始化用户管理器...');
                try {
                    if (typeof this.userManager.init === 'function') {
                        await this.userManager.init();
                        if (this.userManager.initialized) {
                            console.log('✅ 强制初始化成功');
                        } else {
                            console.warn('⚠️ 强制初始化后状态仍未就绪');
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ 强制初始化失败:', error.message);
                }

                // 如果仍然未初始化，返回错误
                if (!this.userManager.initialized) {
                    return {
                        success: false,
                        message: '用户管理器初始化失败，请刷新页面重试'
                    };
                }
            }

            console.log('✅ 用户管理器初始化完成');
        }

        // 检查存储服务
        if (!this.userManager.storageService) {
            return {
                success: false,
                message: '存储服务不可用，请刷新页面重试'
            };
        }

        // 如果存储服务未初始化，等待一段时间
        if (!this.userManager.storageService.initialized) {
            console.log('⏳ 存储服务正在初始化，等待完成...');

            let retryCount = 0;
            const maxRetries = 30; // 最多等待3秒

            while (!this.userManager.storageService.initialized && retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retryCount++;

                // 每秒输出一次等待日志
                if (retryCount % 10 === 0) {
                    console.log(`⏳ 等待存储服务初始化... (${retryCount / 10}秒)`);
                }
            }

            if (!this.userManager.storageService.initialized) {
                return {
                    success: false,
                    message: '存储服务初始化超时，请刷新页面重试'
                };
            }

            console.log('✅ 存储服务初始化完成');
        }

        return {
            success: true,
            message: '系统初始化检查通过'
        };
    }

    /**
     * 处理创建用户
     */
    async handleCreateUser() {
        console.log('🔍 开始创建用户流程...');

        // 检查用户管理器是否可用
        if (!this.userManager) {
            this.showErrorMessage('用户管理器未初始化，请刷新页面重试');
            return;
        }

        // 智能检查用户管理器初始化状态
        const initCheckResult = await this.checkAndWaitForInitialization();
        if (!initCheckResult.success) {
            this.showErrorMessage(initCheckResult.message);
            return;
        }

        const identifierInput = document.getElementById('new-user-identifier');
        const displayNameInput = document.getElementById('new-user-display-name');

        if (!identifierInput || !displayNameInput) {
            this.showErrorMessage('表单元素未找到，请刷新页面重试');
            return;
        }

        const identifier = identifierInput.value.trim();
        const displayName = displayNameInput.value.trim();

        console.log(`📝 用户输入: 标识符="${identifier}", 显示名称="${displayName}"`);

        if (!identifier || !displayName) {
            this.showErrorMessage('请填写完整的用户信息');
            return;
        }

        // 基础格式验证
        if (!/^[a-zA-Z0-9_-]+$/.test(identifier)) {
            this.showErrorMessage('用户标识符只能包含字母、数字、下划线和连字符');
            return;
        }

        if (identifier.length < 3 || identifier.length > 32) {
            this.showErrorMessage('用户标识符长度必须在3-32个字符之间');
            return;
        }

        if (displayName.length < 1 || displayName.length > 50) {
            this.showErrorMessage('显示名称长度必须在1-50个字符之间');
            return;
        }

        try {
            // 显示创建中状态
            const createBtn = document.getElementById('create-user-btn');
            if (createBtn) {
                createBtn.disabled = true;
                createBtn.textContent = '创建中...';
            }

            console.log('🚀 调用用户管理器创建用户...');

            // 创建用户
            const newUser = await this.userManager.createUser(identifier, displayName);

            console.log('✅ 用户创建成功:', newUser);

            // 清空表单
            identifierInput.value = '';
            displayNameInput.value = '';

            // 恢复按钮状态
            if (createBtn) {
                createBtn.disabled = false;
                createBtn.textContent = '创建用户';
            }

            this.showSuccessMessage(`用户创建成功: ${displayName}`);

        } catch (error) {
            console.error('❌ 创建用户失败:', error);
            this.showErrorMessage(`创建用户失败: ${error.message}`);

            // 恢复按钮状态
            const createBtn = document.getElementById('create-user-btn');
            if (createBtn) {
                createBtn.disabled = false;
                createBtn.textContent = '创建用户';
            }
        }
    }

    /**
     * 切换到指定用户
     * @param {string} identifier - 用户标识符
     */
    async switchToUser(identifier) {
        try {
            console.log(`🔄 开始切换到用户: ${identifier}`);

            let switchResult = null;
            if (this.userSwitchManager) {
                switchResult = await this.userSwitchManager.switchUser(identifier);
            } else {
                switchResult = await this.userManager.switchToUser(identifier);
            }

            // 检查切换结果
            if (switchResult && switchResult.success) {
                console.log('✅ 用户切换成功，延迟更新界面显示以确保状态同步');

                // 使用 setTimeout 确保界面更新在用户状态完全同步后执行
                setTimeout(() => {
                    console.log('🔄 执行延迟界面更新');

                    // 更新用户显示
                    this.updateUserDisplay();

                    // 刷新用户列表
                    this.updateUserList();

                    // 通知其他组件用户已切换
                    if (window.screenManager && typeof window.screenManager.updateUserInfo === 'function') {
                        window.screenManager.updateUserInfo();
                    }
                }, 100); // 100ms 延迟确保状态同步

                this.showSuccessMessage(`已切换到用户: ${switchResult.currentUser?.displayName || identifier}`);
            } else {
                console.warn('⚠️ 用户切换可能未完全成功:', switchResult);

                // 即使部分失败，也尝试延迟更新界面
                setTimeout(() => {
                    console.log('🔄 执行延迟界面更新（部分失败情况）');
                    this.updateUserDisplay();
                    this.updateUserList();
                }, 100);

                if (switchResult && switchResult.systemStateError) {
                    this.showWarningMessage(`用户切换完成，但系统状态更新失败: ${switchResult.systemStateError}`);
                } else {
                    this.showWarningMessage('用户切换可能未完全成功，请检查当前用户状态');
                }
            }

            // 关闭模态框
            this.hideModal();

        } catch (error) {
            console.error('❌ 用户切换失败:', error);
            this.showErrorMessage(`切换用户失败: ${error.message}`);
        }
    }

    /**
     * 编辑用户
     * @param {string} identifier - 用户标识符
     */
    async editUser(identifier) {
        const user = this.userManager.users.get(identifier);
        if (!user) {
            this.showErrorMessage('用户不存在');
            return;
        }

        const newDisplayName = prompt('请输入新的显示名称:', user.displayName);
        if (newDisplayName && newDisplayName.trim() !== user.displayName) {
            try {
                await this.userManager.updateUserDisplayName(identifier, newDisplayName.trim());
                this.updateUserList();
                this.updateUserDisplay();
            } catch (error) {
                this.showErrorMessage(`更新显示名称失败: ${error.message}`);
            }
        }
    }

    /**
     * 删除用户
     * @param {string} identifier - 用户标识符
     */
    async deleteUser(identifier) {
        const user = this.userManager.users.get(identifier);
        if (!user) {
            this.showErrorMessage('用户不存在');
            return;
        }

        const confirmMessage = `确定要删除用户 "${user.displayName}" (${identifier}) 吗？\n\n此操作将永久删除该用户的所有数据，且无法撤销。`;

        if (confirm(confirmMessage)) {
            try {
                await this.userManager.deleteUser(identifier);
                this.updateUserList();
            } catch (error) {
                this.showErrorMessage(`删除用户失败: ${error.message}`);
            }
        }
    }

    /**
     * 导出当前用户数据
     */
    async exportCurrentUser() {
        try {
            const currentUser = this.userManager.getCurrentUser();
            if (!currentUser) {
                this.showErrorMessage('没有当前用户');
                return;
            }

            const exportData = await this.userManager.exportUserData(currentUser.identifier);

            // 创建下载链接
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `瞬光捕手_用户数据_${currentUser.identifier}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);

            this.showSuccessMessage('用户数据导出成功');

        } catch (error) {
            this.showErrorMessage(`导出用户数据失败: ${error.message}`);
        }
    }

    /**
     * 显示导入对话框
     */
    showImportDialog() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (file) {
                this.importUserData(file);
            }
        };
        input.click();
    }

    /**
     * 导入用户数据
     * @param {File} file - 用户数据文件
     */
    async importUserData(file) {
        try {
            const text = await file.text();
            const importData = JSON.parse(text);

            const success = await this.userManager.importUserData(importData, {
                overwrite: confirm('如果用户已存在，是否覆盖现有数据？')
            });

            if (success) {
                this.updateUserList();
                this.showSuccessMessage('用户数据导入成功');
            } else {
                this.showErrorMessage('用户数据导入失败');
            }

        } catch (error) {
            this.showErrorMessage(`导入用户数据失败: ${error.message}`);
        }
    }

    /**
     * 显示数据统计
     */
    async showDataStats() {
        try {
            const currentUser = this.userManager.getCurrentUser();
            if (!currentUser) {
                this.showErrorMessage('没有当前用户');
                return;
            }

            const stats = await this.userManager.getUserStorageUsage(currentUser.identifier);

            if (stats) {
                const statsMessage = `
用户: ${currentUser.displayName} (${currentUser.identifier})
数据键数量: ${stats.totalKeys}
数据总大小: ${(stats.totalSize / 1024).toFixed(2)} KB

数据类型分布:
${stats.keyDetails.map(detail =>
    `- ${detail.dataType}: ${(detail.size / 1024).toFixed(2)} KB`
).join('\n')}
                `.trim();

                alert(statsMessage);
            }

        } catch (error) {
            this.showErrorMessage(`获取数据统计失败: ${error.message}`);
        }
    }

    /**
     * 显示成功消息
     * @param {string} message - 消息内容
     */
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    /**
     * 显示错误消息
     * @param {string} message - 消息内容
     */
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    /**
     * 显示警告消息
     * @param {string} message - 消息内容
     */
    showWarningMessage(message) {
        this.showMessage(message, 'warning');
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `message-toast ${type}`;
        messageElement.textContent = message;

        // 添加样式
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // 根据类型设置背景色
        switch (type) {
            case 'success':
                messageElement.style.background = '#4CAF50';
                break;
            case 'error':
                messageElement.style.background = '#f44336';
                break;
            default:
                messageElement.style.background = '#2196F3';
        }

        document.body.appendChild(messageElement);

        // 自动移除
        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => messageElement.remove(), 300);
            }
        }, 3000);
    }
}

// 创建全局用户管理界面实例
if (typeof window !== 'undefined') {
    window.UserManagementUI = UserManagementUI;
    window.userManagementUI = new UserManagementUI();
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserManagementUI;
}
