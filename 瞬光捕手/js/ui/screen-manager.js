/**
 * 瞬光捕手 - 屏幕管理器
 * 负责不同界面之间的切换和管理
 */

class ScreenManager {
    constructor() {
        this.currentScreen = 'loading';
        this.screens = new Map();
        this.overlays = new Map();
        this.initialized = false;
    }

    /**
     * 初始化屏幕管理器
     */
    async init() {
        try {
            // 注册所有屏幕
            this.registerScreens();
            
            // 注册所有覆盖层
            this.registerOverlays();
            
            // 绑定事件监听器
            this.bindEvents();
            
            this.initialized = true;
            console.log('屏幕管理器初始化完成');

            // 显示主菜单
            setTimeout(() => {
                this.showScreen('main-menu');
                // 初始化用户信息显示
                this.updateUserDisplayInAllScreens();
            }, 1000);
            
        } catch (error) {
            console.error('屏幕管理器初始化失败:', error);
        }
    }

    /**
     * 注册所有屏幕
     */
    registerScreens() {
        const screenIds = [
            'loading-screen',
            'main-menu',
            'game-screen',
            'leaderboard-screen',
            'custom-levels-screen',
            'level-editor-screen',
            'settings-screen'
        ];

        screenIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.screens.set(id, element);
            }
        });
    }

    /**
     * 注册所有覆盖层
     */
    registerOverlays() {
        const overlayIds = [
            'pause-menu',
            'game-over',
            'level-detail-dialog'
        ];

        overlayIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.overlays.set(id, element);
            }
        });
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主菜单按钮
        this.bindButton('start-game-btn', () => this.startGame());
        this.bindButton('level-editor-btn', () => this.showLevelEditor());
        this.bindButton('custom-levels-btn', () => this.showCustomLevels());
        this.bindButton('leaderboard-btn', () => this.showLeaderboard());
        this.bindButton('settings-btn', () => this.showScreen('settings-screen'));

        // 游戏控制按钮
        this.bindButton('pause-btn', () => this.pauseGame());
        this.bindButton('resume-btn', () => this.resumeGame());
        this.bindButton('restart-btn', () => this.restartGame());
        this.bindButton('main-menu-btn', () => this.backToMainMenu());
        this.bindButton('back-to-main-menu-btn', () => this.backToMainMenu());

        // 游戏结束按钮
        this.bindButton('play-again-btn', () => this.restartGame());
        this.bindButton('back-to-menu-btn', () => this.backToMainMenu());

        // 排行榜按钮
        this.bindButton('refresh-leaderboard-btn', () => this.refreshLeaderboard());
        this.bindButton('back-from-leaderboard-btn', () => this.showScreen('main-menu'));

        // 自定义关卡按钮
        this.bindButton('back-from-custom-levels-btn', () => this.showScreen('main-menu'));
        this.bindButton('create-first-level-btn', () => this.showLevelEditor());
        this.bindCustomLevelsButtons();

        // 关卡编辑器按钮
        this.bindButton('back-from-editor-btn', () => this.showScreen('main-menu'));
        this.bindEditorButtons();

        // 设置按钮
        this.bindButton('save-settings-btn', () => this.saveSettings());
        this.bindButton('cancel-settings-btn', () => this.showScreen('main-menu'));

        // 用户管理按钮
        this.bindButton('user-management-btn', () => this.showUserManagement());

        // 监听用户切换事件，更新界面显示
        this.setupUserSwitchListeners();

        // 语言选择
        const languageSelect = document.getElementById('language-select');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                if (window.i18nService && typeof i18nService.setLanguage === 'function') {
                    i18nService.setLanguage(e.target.value);
                }
            });
        }

        // 难度选择
        const difficultySelect = document.getElementById('difficulty-select');
        if (difficultySelect) {
            difficultySelect.addEventListener('change', (e) => {
                this.handleDifficultyChange(e.target.value);
            });
        }

        // 音量控制
        this.bindVolumeControl('sound-volume', 'sound-value');
        this.bindVolumeControl('music-volume', 'music-value');

        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));

        // 触摸控制
        const touchArea = document.getElementById('touch-area');
        if (touchArea) {
            touchArea.addEventListener('touchstart', (e) => {
                e.preventDefault();
                if (window.gameEngine && gameEngine.gameState === 'playing') {
                    const rect = gameEngine.canvas.getBoundingClientRect();
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    gameEngine.handleClick(centerX, centerY);
                }
            });
        }
    }

    /**
     * 绑定按钮事件 - 增强触摸支持
     */
    bindButton(buttonId, callback) {
        const button = document.getElementById(buttonId);
        if (button) {
            console.log(`✅ 绑定按钮事件: ${buttonId}`);
            // 添加触摸友好的事件处理
            this.addTouchFriendlyEvents(button, callback);
        } else {
            console.warn(`⚠️ 按钮未找到: ${buttonId}`);
        }
    }

    /**
     * 为按钮添加触摸友好的事件处理
     */
    addTouchFriendlyEvents(element, callback) {
        if (window.touchHelper) {
            // 使用全局TouchHelper工具
            window.touchHelper.addTouchFriendlyEvents(element, callback);
        } else {
            // 降级到基本的点击事件
            console.warn('⚠️ TouchHelper未加载，使用基本点击事件');
            element.addEventListener('click', callback);
        }
    }

    /**
     * 绑定音量控制
     */
    bindVolumeControl(sliderId, valueId) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);

        if (slider && valueDisplay) {
            slider.addEventListener('input', (e) => {
                const value = e.target.value;
                valueDisplay.textContent = value + '%';
            });
        }
    }

    /**
     * 绑定自定义关卡按钮
     */
    bindCustomLevelsButtons() {
        // 关卡详情对话框按钮
        this.bindButton('close-level-detail-btn', () => this.hideOverlay('level-detail-dialog'));
        this.bindButton('play-level-btn', () => this.playSelectedLevel());
        this.bindButton('edit-level-btn', () => this.editSelectedLevel());
        this.bindButton('delete-level-btn', () => this.deleteSelectedLevel());
        this.bindButton('like-level-btn', () => this.rateLevelLevel(true));
        this.bindButton('dislike-level-btn', () => this.rateLevelLevel(false));
    }

    /**
     * 绑定关卡编辑器按钮
     */
    bindEditorButtons() {
        // 编辑器操作按钮
        this.bindButton('new-level-btn', () => this.createNewLevel());
        this.bindButton('load-level-btn', () => this.showLoadLevelDialog());
        this.bindButton('save-level-btn', () => this.saveCurrentLevel());
        this.bindButton('test-level-btn', () => this.testCurrentLevel());
        this.bindButton('publish-level-btn', () => this.publishCurrentLevel());

        // 视图控制
        const showGridCheckbox = document.getElementById('show-grid');
        if (showGridCheckbox) {
            showGridCheckbox.addEventListener('change', (e) => {
                if (window.levelEditor && levelEditor.initialized) {
                    levelEditor.editorState.showGrid = e.target.checked;
                    levelEditor.render();
                }
            });
        }

        const snapToGridCheckbox = document.getElementById('snap-to-grid');
        if (snapToGridCheckbox) {
            snapToGridCheckbox.addEventListener('change', (e) => {
                if (window.levelEditor && levelEditor.initialized) {
                    levelEditor.editorState.snapToGrid = e.target.checked;
                }
            });
        }

        // 缩放控制
        this.bindButton('zoom-in-btn', () => this.zoomIn());
        this.bindButton('zoom-out-btn', () => this.zoomOut());

        // 关卡加载对话框
        this.bindButton('load-selected-btn', () => this.loadSelectedLevel());
        this.bindButton('cancel-load-btn', () => this.hideLoadLevelDialog());

        // 关卡设置
        const timeLimitInput = document.getElementById('time-limit');
        if (timeLimitInput) {
            timeLimitInput.addEventListener('input', (e) => {
                if (window.levelEditor && levelEditor.currentLevel) {
                    levelEditor.currentLevel.settings.timeLimit = parseInt(e.target.value);
                    levelEditor.markAsModified();
                }
            });
        }

        const targetScoreInput = document.getElementById('target-score');
        if (targetScoreInput) {
            targetScoreInput.addEventListener('input', (e) => {
                if (window.levelEditor && levelEditor.currentLevel) {
                    levelEditor.currentLevel.settings.targetScore = parseInt(e.target.value);
                    levelEditor.markAsModified();
                }
            });
        }
    }

    /**
     * 显示指定屏幕
     */
    showScreen(screenId) {
        // 隐藏当前屏幕
        if (this.currentScreen && this.screens.has(this.currentScreen)) {
            const currentScreenElement = this.screens.get(this.currentScreen);
            currentScreenElement.classList.add('hidden');
            currentScreenElement.classList.remove('active');
        }

        // 显示新屏幕
        if (this.screens.has(screenId)) {
            const newScreenElement = this.screens.get(screenId);
            newScreenElement.classList.remove('hidden');
            newScreenElement.classList.add('active');
            this.currentScreen = screenId;

            console.log(`📱 屏幕切换: ${this.currentScreen} → ${screenId}`);

            // 屏幕切换后的特殊处理
            this.onScreenChanged(screenId);
        }
    }

    /**
     * 显示覆盖层
     */
    showOverlay(overlayId) {
        if (this.overlays.has(overlayId)) {
            this.overlays.get(overlayId).classList.remove('hidden');
        }
    }

    /**
     * 隐藏覆盖层
     */
    hideOverlay(overlayId) {
        if (this.overlays.has(overlayId)) {
            this.overlays.get(overlayId).classList.add('hidden');
        }
    }

    /**
     * 隐藏所有覆盖层
     */
    hideAllOverlays() {
        this.overlays.forEach(overlay => {
            overlay.classList.add('hidden');
        });
    }

    /**
     * 屏幕切换后的处理
     */
    onScreenChanged(screenId) {
        switch (screenId) {
            case 'main-menu':
                // 停止游戏
                if (gameEngine.gameState === 'playing') {
                    gameEngine.pauseGame();
                }
                this.hideAllOverlays();
                // 更新用户信息显示
                this.updateUserDisplayInAllScreens();
                break;
                
            case 'game-screen':
                // 确保游戏画布正确调整大小
                if (gameEngine.initialized) {
                    gameEngine.resizeCanvas();
                }
                break;
                
            case 'custom-levels-screen':
                // 刷新关卡列表
                if (levelManager.initialized) {
                    this.loadCustomLevelsList();
                }
                break;

            case 'level-editor-screen':
                // 确保编辑器画布正确调整大小
                if (levelEditor.initialized) {
                    levelEditor.handleResize();
                    levelEditor.render();
                }
                break;

            case 'settings-screen':
                // 加载当前设置
                this.loadCurrentSettings();
                break;
        }
    }

    /**
     * 开始游戏
     */
    startGame() {
        this.showScreen('game-screen');
        if (gameEngine.initialized) {
            gameEngine.startGame();
        }
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        if (gameEngine.gameState === 'playing') {
            gameEngine.pauseGame();
            this.showOverlay('pause-menu');
        }
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        this.hideOverlay('pause-menu');
        if (gameEngine.gameState === 'paused') {
            gameEngine.resumeGame();
        }
    }

    /**
     * 重新开始游戏
     */
    restartGame() {
        this.hideAllOverlays();
        this.showScreen('game-screen');
        if (gameEngine.initialized) {
            gameEngine.startGame();
        }
    }

    /**
     * 返回主菜单
     */
    backToMainMenu() {
        console.log('🏠 返回主菜单被调用');

        // 停止游戏循环和重置游戏状态
        if (window.gameEngine) {
            console.log('🎮 当前游戏状态:', gameEngine.gameState);

            if (gameEngine.gameState === 'playing') {
                gameEngine.pauseGame();
                console.log('⏸️ 游戏已暂停');
            }

            // 重置游戏状态为菜单
            gameEngine.gameState = 'menu';
            console.log('🔄 游戏状态已重置为菜单');
        }

        this.hideAllOverlays();

        // 检查是否在游戏选择器环境中
        if (window.parent && window.parent !== window) {
            // 在iframe中，通知父页面返回游戏选择界面
            try {
                window.parent.postMessage({
                    type: 'RETURN_TO_GAME_SELECTOR',
                    game: 'split-second-spark'
                }, '*');
                console.log('🏠 通知父页面返回游戏选择器');
            } catch (error) {
                console.warn('⚠️ 无法通知父页面返回游戏选择器:', error);
                // 备用方案：直接跳转到主页面
                window.top.location.href = '../index.html';
            }
        } else {
            // 直接访问游戏页面，显示游戏内主菜单
            console.log('🏠 切换到主菜单界面');
            this.showScreen('main-menu');
        }
    }

    /**
     * 显示关卡编辑器
     */
    async showLevelEditor() {
        this.showScreen('level-editor-screen');

        // 初始化关卡编辑器
        if (!levelEditor.initialized) {
            await levelEditor.init();
        }

        // 开始编辑模式
        levelEditor.startEditing();

        // 创建新关卡或加载现有关卡
        if (!levelEditor.currentLevel) {
            levelEditor.createNewLevel();
        }
    }

    /**
     * 显示自定义关卡
     */
    async showCustomLevels() {
        this.showScreen('custom-levels-screen');

        // 初始化关卡管理器
        if (!levelManager.initialized) {
            await levelManager.init();
        }

        // 加载自定义关卡列表
        await this.loadCustomLevelsList();

        // 绑定控制事件
        this.bindCustomLevelsControls();
    }

    /**
     * 显示排行榜
     */
    showLeaderboard() {
        console.log('🏆 显示排行榜界面...');
        this.showScreen('leaderboard-screen');
        this.loadLeaderboard();
    }

    /**
     * 加载排行榜数据
     */
    async loadLeaderboard() {
        console.log('📊 开始加载排行榜数据...');

        // 检查排行榜管理器是否存在
        if (!window.leaderboardManager) {
            console.error('❌ 排行榜管理器未找到');
            this.showLeaderboardError('排行榜管理器未初始化');
            return;
        }

        // 检查排行榜管理器是否已初始化
        if (!window.leaderboardManager.initialized) {
            console.log('⏳ 排行榜管理器未初始化，尝试初始化...');
            try {
                await window.leaderboardManager.init();
                console.log('✅ 排行榜管理器初始化成功');
            } catch (error) {
                console.error('❌ 排行榜管理器初始化失败:', error);
                this.showLeaderboardError('排行榜管理器初始化失败');
                return;
            }
        }

        const currentType = this.getCurrentLeaderboardType();
        console.log(`📋 加载排行榜类型: ${currentType}`);
        await this.displayLeaderboard(currentType);
        this.bindLeaderboardTabs();
    }

    /**
     * 获取当前排行榜类型
     */
    getCurrentLeaderboardType() {
        const activeTab = document.querySelector('.tab-btn.active');
        return activeTab ? activeTab.dataset.type : 'global_high_score';
    }

    /**
     * 绑定排行榜标签页事件 - 增强触摸支持
     */
    bindLeaderboardTabs() {
        const tabButtons = document.querySelectorAll('.tab-btn');

        tabButtons.forEach(button => {
            this.addTouchFriendlyEvents(button, async (e) => {
                // 移除所有活动状态
                tabButtons.forEach(btn => btn.classList.remove('active'));

                // 设置当前按钮为活动状态
                button.classList.add('active');

                // 显示对应的排行榜
                const type = button.dataset.type;
                await this.displayLeaderboard(type);
            });
        });
    }

    /**
     * 显示排行榜错误信息
     */
    showLeaderboardError(message) {
        const entriesContainer = document.getElementById('leaderboard-entries');
        const playerRankDisplay = document.getElementById('player-rank-display');

        if (entriesContainer) {
            entriesContainer.innerHTML = `
                <div class="empty-leaderboard">
                    <div class="icon">❌</div>
                    <p>排行榜加载失败</p>
                    <p>${message}</p>
                    <button onclick="screenManager.refreshLeaderboard()" class="retry-btn">重试</button>
                </div>
            `;
        }

        if (playerRankDisplay) {
            playerRankDisplay.innerHTML = `
                <p>排行榜数据不可用</p>
            `;
        }
    }

    /**
     * 显示指定类型的排行榜
     */
    async displayLeaderboard(type) {
        console.log(`🎯 显示排行榜: ${type}`);

        const entriesContainer = document.getElementById('leaderboard-entries');
        const playerRankDisplay = document.getElementById('player-rank-display');

        if (!entriesContainer) {
            console.error('❌ 排行榜容器元素未找到');
            return;
        }

        // 显示加载状态
        entriesContainer.innerHTML = `
            <div class="loading-leaderboard">
                <div class="spinner"></div>
                <p data-i18n="leaderboard.loading">加载中...</p>
            </div>
        `;

        try {
            // 检查排行榜管理器
            if (!window.leaderboardManager) {
                throw new Error('排行榜管理器不可用');
            }

            console.log(`📊 获取排行榜数据: ${type}`);
            const leaderboard = window.leaderboardManager.getLeaderboard(type, 20);
            console.log('📋 排行榜数据:', leaderboard);

            if (!leaderboard || leaderboard.entries.length === 0) {
                console.log('📭 排行榜为空，显示空状态');
                entriesContainer.innerHTML = `
                    <div class="empty-leaderboard">
                        <div class="icon">🏆</div>
                        <p data-i18n="leaderboard.empty">暂无排行榜数据</p>
                        <p data-i18n="leaderboard.beFirst">成为第一个上榜的玩家吧！</p>
                    </div>
                `;

                if (playerRankDisplay) {
                    playerRankDisplay.innerHTML = `
                        <p data-i18n="leaderboard.notRanked">您还未上榜</p>
                    `;
                }
                return;
            }

            // 显示排行榜条目
            console.log(`📊 渲染 ${leaderboard.entries.length} 个排行榜条目`);

            let currentPlayer = null;
            try {
                currentPlayer = window.playerManager ? window.playerManager.getCurrentPlayer() : null;
            } catch (error) {
                console.warn('⚠️ 获取当前玩家失败:', error);
            }

            let entriesHtml = '';

            leaderboard.entries.forEach((entry, index) => {
                const rank = index + 1;
                const isCurrentPlayer = currentPlayer && entry.playerId === currentPlayer.id;
                const rankClass = rank <= 3 ? `rank-${rank} top-3` : '';

                // 安全地格式化分数和时间
                let formattedScore = entry.score;
                let formattedTime = new Date(entry.timestamp).toLocaleDateString();

                try {
                    if (window.leaderboardManager && window.leaderboardManager.formatScore) {
                        formattedScore = window.leaderboardManager.formatScore(entry.score);
                    }
                    if (window.leaderboardManager && window.leaderboardManager.formatTime) {
                        formattedTime = window.leaderboardManager.formatTime(entry.timestamp);
                    }
                } catch (error) {
                    console.warn('⚠️ 格式化分数或时间失败:', error);
                }

                entriesHtml += `
                    <div class="leaderboard-entry ${isCurrentPlayer ? 'current-player' : ''}">
                        <div class="rank ${rankClass}">${rank}</div>
                        <div class="player-name">
                            <div class="name">${entry.playerName || '未知玩家'}</div>
                            <div class="details">
                                关卡 ${entry.level || 1} |
                                连击 ${entry.combo || 0}
                            </div>
                        </div>
                        <div class="score">${formattedScore}</div>
                        <div class="time">${formattedTime}</div>
                    </div>
                `;
            });

            entriesContainer.innerHTML = entriesHtml;

            // 显示玩家排名信息
            if (playerRankDisplay && currentPlayer) {
                try {
                    const playerRank = window.leaderboardManager.getPlayerRank(type, currentPlayer.id);
                    console.log('👤 玩家排名信息:', playerRank);

                    if (playerRank) {
                        let formattedScore = playerRank.entry.score;
                        try {
                            if (window.leaderboardManager.formatScore) {
                                formattedScore = window.leaderboardManager.formatScore(playerRank.entry.score);
                            }
                        } catch (error) {
                            console.warn('⚠️ 格式化玩家分数失败:', error);
                        }

                        playerRankDisplay.innerHTML = `
                            <p>
                                <span data-i18n="leaderboard.yourRank">您的排名:</span>
                                <span class="rank-text">#${playerRank.rank}</span>
                                <span data-i18n="leaderboard.outOf">/ ${playerRank.totalEntries}</span>
                            </p>
                            <p>
                                <span data-i18n="leaderboard.yourScore">您的分数:</span>
                                <span class="rank-text">${formattedScore}</span>
                            </p>
                        `;
                    } else {
                        playerRankDisplay.innerHTML = `
                            <p data-i18n="leaderboard.notRanked">您还未上榜，快去游戏获得分数吧！</p>
                        `;
                    }
                } catch (error) {
                    console.warn('⚠️ 获取玩家排名失败:', error);
                    playerRankDisplay.innerHTML = `
                        <p>排名信息不可用</p>
                    `;
                }
            } else if (playerRankDisplay) {
                playerRankDisplay.innerHTML = `
                    <p>请先创建玩家档案</p>
                `;
            }

            // 更新国际化文本
            try {
                if (window.i18nService && window.i18nService.updatePageText) {
                    window.i18nService.updatePageText();
                }
            } catch (error) {
                console.warn('⚠️ 更新国际化文本失败:', error);
            }

            console.log('✅ 排行榜显示完成');

        } catch (error) {
            console.error('❌ 显示排行榜失败:', error);
            entriesContainer.innerHTML = `
                <div class="empty-leaderboard">
                    <div class="icon">❌</div>
                    <p data-i18n="leaderboard.error">加载排行榜失败</p>
                    <p>${error.message}</p>
                    <button onclick="screenManager.refreshLeaderboard()" class="retry-btn">重试</button>
                </div>
            `;
        }
    }

    /**
     * 刷新排行榜
     */
    async refreshLeaderboard() {
        const currentType = this.getCurrentLeaderboardType();
        await this.displayLeaderboard(currentType);
    }

    /**
     * 显示用户管理
     */
    showUserManagement() {
        console.log('🔍 尝试显示用户管理界面...');
        console.log('userManagementUI存在:', !!window.userManagementUI);
        console.log('userManagementUI已初始化:', window.userManagementUI?.initialized);

        if (window.userManagementUI && window.userManagementUI.initialized) {
            console.log('✅ 调用用户管理界面显示方法');
            window.userManagementUI.showModal();
        } else {
            console.warn('⚠️ 用户管理界面未初始化');
            console.log('userManagementUI:', window.userManagementUI);

            // 尝试手动初始化
            if (window.userManagementUI && !window.userManagementUI.initialized) {
                console.log('🔧 尝试手动初始化用户管理界面...');
                window.userManagementUI.init().then(() => {
                    console.log('✅ 手动初始化完成，显示模态框');
                    window.userManagementUI.showModal();
                }).catch(error => {
                    console.error('❌ 手动初始化失败:', error);
                });
            } else {
                console.error('❌ 用户管理界面实例不存在');
            }
        }
    }

    /**
     * 设置用户切换事件监听
     */
    setupUserSwitchListeners() {
        // 监听用户切换完成事件
        window.addEventListener('userSwitch:completed', (event) => {
            // 添加延迟确保用户状态完全同步
            setTimeout(() => {
                console.log('🔄 响应用户切换完成事件，更新界面显示');
                this.updateUserDisplayInAllScreens();
            }, 50);
        });

        // 监听用户管理器的用户切换事件 - 使用与其他组件一致的方式
        this.setupUserManagerEventListeners();
    }

    /**
     * 设置用户管理器事件监听器
     */
    setupUserManagerEventListeners() {
        console.log('🔄 ScreenManager 设置用户管理器事件监听器');

        // 获取正确的用户管理器实例
        let userManager = null;
        if (window.userSystemAdapter && window.userSystemAdapter.getUserManager) {
            userManager = window.userSystemAdapter.getUserManager();
            console.log('📋 ScreenManager 通过适配器获取用户管理器进行事件监听');
        } else if (window.userManager) {
            userManager = window.userManager;
            console.log('📋 ScreenManager 使用备用用户管理器进行事件监听');
        }

        if (userManager) {
            // 监听用户切换事件
            userManager.addEventListener('userSwitched', (eventData) => {
                setTimeout(() => {
                    console.log('🔄 ScreenManager 响应用户切换事件，更新界面显示');
                    this.updateUserDisplayInAllScreens();
                }, 50);
            });

            // 监听用户创建事件
            userManager.addEventListener('userCreated', (eventData) => {
                setTimeout(() => {
                    console.log('🔄 ScreenManager 响应用户创建事件，更新界面显示');
                    this.updateUserDisplayInAllScreens();
                }, 50);
            });

            console.log('✅ ScreenManager 用户管理器事件监听器设置完成');
        } else {
            console.warn('⚠️ ScreenManager 未找到用户管理器，无法设置事件监听器');
        }
    }

    /**
     * 更新所有界面中的用户信息显示
     */
    updateUserDisplayInAllScreens() {
        console.log('🔍 ScreenManager 开始更新所有界面用户信息...');

        // 获取当前用户信息 - 使用与 UserManagementUI 相同的方式
        let currentUser = null;
        let displayName = '游客';
        let userType = 'guest';
        let userManager = null;

        // 优先从 userSystemAdapter 获取最新实例（与 UserManagementUI 保持一致）
        if (window.userSystemAdapter && window.userSystemAdapter.getUserManager) {
            userManager = window.userSystemAdapter.getUserManager();
            console.log(`📋 ScreenManager 通过适配器获取用户管理器: ${userManager ? userManager.constructor.name : 'null'}`);

            // 验证与 UserManagementUI 的实例一致性
            if (window.userManagementUI && window.userManagementUI.userManager) {
                const uiUserManager = window.userManagementUI.userManager;
                const isSameInstance = userManager === uiUserManager;
                console.log(`📋 ScreenManager 实例一致性检查: 与 UserManagementUI ${isSameInstance ? '✅ 相同' : '❌ 不同'}`);

                if (!isSameInstance) {
                    console.warn('⚠️ ScreenManager 检测到与 UserManagementUI 实例不一致');
                }
            }
        } else if (window.userManager) {
            userManager = window.userManager;
            console.log(`📋 ScreenManager 使用备用用户管理器: ${userManager ? userManager.constructor.name : 'null'}`);
        }

        if (userManager && userManager.getCurrentUser) {
            currentUser = userManager.getCurrentUser();
            console.log(`📋 ScreenManager 获取到的当前用户对象:`, currentUser);

            if (currentUser) {
                displayName = currentUser.displayName;
                userType = currentUser.type;
                console.log(`📋 ScreenManager 解析用户信息: ${displayName} (${userType}) [${currentUser.identifier}]`);
            } else {
                console.log(`⚠️ ScreenManager 用户管理器返回 null`);
            }
        } else {
            console.log(`⚠️ ScreenManager 未找到有效的用户管理器或 getCurrentUser 方法`);
        }

        console.log(`🔄 ScreenManager 更新所有界面用户信息: ${displayName} (${userType})`);

        // 更新主菜单中的用户名显示
        const currentUserName = document.getElementById('current-user-name');
        if (currentUserName) {
            currentUserName.textContent = displayName;
            console.log(`✅ ScreenManager 已更新主菜单用户名显示: ${displayName}`);
        } else {
            console.log(`⚠️ ScreenManager 未找到主菜单用户名元素: current-user-name`);
        }

        // 更新游戏界面中的用户信息显示
        const userNameDisplay = document.getElementById('user-name-display');
        const userTypeBadge = document.getElementById('user-type-badge');

        if (userNameDisplay) {
            userNameDisplay.textContent = displayName;
            console.log(`✅ ScreenManager 已更新游戏界面用户名显示: ${displayName}`);
        } else {
            console.log(`⚠️ ScreenManager 未找到游戏界面用户名元素: user-name-display`);
        }

        if (userTypeBadge) {
            userTypeBadge.textContent = userType;
            userTypeBadge.className = `user-type-badge ${userType}`;
            console.log(`✅ ScreenManager 已更新用户类型标识: ${userType}`);
        } else {
            console.log(`⚠️ ScreenManager 未找到用户类型标识元素: user-type-badge`);
        }

        // 同时更新旧系统的显示（保持兼容性）
        const currentPlayerName = document.getElementById('current-player-name');
        if (currentPlayerName) {
            currentPlayerName.textContent = displayName;
            console.log(`✅ ScreenManager 已更新旧系统用户名显示: ${displayName}`);
        } else {
            console.log(`⚠️ ScreenManager 未找到旧系统用户名元素: current-player-name`);
        }
    }



    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            const soundVolume = document.getElementById('sound-volume')?.value || 50;
            const musicVolume = document.getElementById('music-volume')?.value || 30;
            const language = document.getElementById('language-select')?.value || 'zh-CN';
            const difficulty = document.getElementById('difficulty-select')?.value || 'normal';

            // 保存到玩家设置
            const player = playerManager.getCurrentPlayer();
            if (!player.isGuest) {
                await playerManager.updatePlayerSettings({
                    soundVolume: parseInt(soundVolume),
                    musicVolume: parseInt(musicVolume),
                    language: language,
                    difficulty: difficulty
                });
            }

            // 应用语言设置
            await i18nService.setLanguage(language);

            // 应用难度设置
            if (window.difficultyConfigManager) {
                await difficultyConfigManager.setDifficulty(difficulty);
            }

            // 显示成功消息
            const successMessage = window.i18nService ?
                i18nService.t('settings.saved.success') : '设置已保存';
            this.showNotification(successMessage, 'success');

            // 延迟返回主菜单，让用户看到成功消息
            setTimeout(() => {
                this.showScreen('main-menu');
            }, 1500);

        } catch (error) {
            console.error('保存设置失败:', error);
            const errorMessage = window.i18nService ?
                i18nService.t('settings.saved.error') : '保存设置失败';
            this.showNotification(errorMessage, 'error');
        }
    }

    /**
     * 加载当前设置
     */
    loadCurrentSettings() {
        const player = playerManager.getCurrentPlayer();

        // 加载音量设置
        const soundSlider = document.getElementById('sound-volume');
        const musicSlider = document.getElementById('music-volume');
        const soundValue = document.getElementById('sound-value');
        const musicValue = document.getElementById('music-value');

        if (soundSlider && soundValue) {
            const volume = player.settings?.soundVolume || 50;
            soundSlider.value = volume;
            soundValue.textContent = volume + '%';
        }

        if (musicSlider && musicValue) {
            const volume = player.settings?.musicVolume || 30;
            musicSlider.value = volume;
            musicValue.textContent = volume + '%';
        }

        // 加载难度设置
        const difficultySelect = document.getElementById('difficulty-select');
        if (difficultySelect && window.difficultyConfigManager) {
            const currentDifficulty = difficultyConfigManager.getCurrentDifficulty();
            difficultySelect.value = currentDifficulty;
            this.updateDifficultyDescription(currentDifficulty);
        }
    }

    /**
     * 处理难度变更
     * @param {string} difficulty - 新的难度等级
     */
    async handleDifficultyChange(difficulty) {
        try {
            if (window.difficultyConfigManager) {
                const success = await difficultyConfigManager.setDifficulty(difficulty);
                if (success) {
                    console.log(`🎯 难度已更改为: ${difficulty}`);
                    this.updateDifficultyDescription(difficulty);

                    // 显示成功提示
                    const successMessage = window.i18nService ?
                        i18nService.t('difficulty.changed.success', { difficulty: this.getDifficultyDisplayName(difficulty) }) :
                        `难度已更改为: ${this.getDifficultyDisplayName(difficulty)}`;
                    this.showNotification(successMessage, 'success');
                } else {
                    console.error('❌ 难度更改失败');
                    const errorMessage = window.i18nService ?
                        i18nService.t('difficulty.changed.error') : '难度更改失败';
                    this.showNotification(errorMessage, 'error');
                }
            }
        } catch (error) {
            console.error('❌ 难度更改过程中发生错误:', error);
            this.showNotification('难度更改失败', 'error');
        }
    }

    /**
     * 更新难度描述
     * @param {string} difficulty - 难度等级
     */
    updateDifficultyDescription(difficulty) {
        const descriptionElement = document.getElementById('difficulty-description-text');
        if (!descriptionElement || !window.difficultyConfigManager) return;

        const difficultyInfo = difficultyConfigManager.difficultyLevels[difficulty];
        if (difficultyInfo) {
            descriptionElement.textContent = difficultyInfo.description;

            // 更新描述框的颜色
            const descriptionContainer = descriptionElement.closest('.difficulty-description');
            if (descriptionContainer) {
                descriptionContainer.style.borderLeftColor = difficultyInfo.color;
            }
        }
    }

    /**
     * 获取难度显示名称
     * @param {string} difficulty - 难度等级
     * @returns {string} 显示名称
     */
    getDifficultyDisplayName(difficulty) {
        if (!window.difficultyConfigManager) return difficulty;

        const difficultyInfo = difficultyConfigManager.difficultyLevels[difficulty];
        return difficultyInfo ? difficultyInfo.name : difficulty;
    }

    /**
     * 显示通知消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 ('success', 'error', 'warning', 'info')
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // 添加样式
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease',
            maxWidth: '300px',
            wordWrap: 'break-word'
        });

        // 设置背景颜色
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            warning: '#FF9800',
            info: '#2196F3'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);

        // 加载语言设置
        const languageSelect = document.getElementById('language-select');
        if (languageSelect) {
            languageSelect.value = i18nService.getCurrentLanguage();
        }
    }

    /**
     * 处理键盘输入
     */
    handleKeyDown(event) {
        switch (event.code) {
            case 'Escape':
                if (this.currentScreen === 'game-screen') {
                    if (gameEngine.gameState === 'playing') {
                        this.pauseGame();
                    } else if (gameEngine.gameState === 'paused') {
                        this.resumeGame();
                    }
                } else {
                    // 在其他界面按ESC返回主菜单
                    this.backToMainMenu();
                }
                break;
                
            case 'Space':
                if (this.currentScreen === 'game-screen' && gameEngine.gameState === 'playing') {
                    event.preventDefault();
                    // 空格键作为点击输入
                    const centerX = gameEngine.canvas.width / 2;
                    const centerY = gameEngine.canvas.height / 2;
                    gameEngine.handleClick(centerX, centerY);
                }
                break;
                
            case 'Enter':
                // 在用户管理界面按回车可以快速创建用户（由用户管理UI处理）
                break;
        }
    }

    /**
     * 获取当前屏幕
     */
    getCurrentScreen() {
        return this.currentScreen;
    }

    /**
     * 检查覆盖层是否显示
     */
    isOverlayVisible(overlayId) {
        const overlay = this.overlays.get(overlayId);
        return overlay && !overlay.classList.contains('hidden');
    }

    // 关卡编辑器相关方法

    /**
     * 创建新关卡
     */
    createNewLevel() {
        if (levelEditor.initialized) {
            levelEditor.createNewLevel();
            this.updateEditorUI();
        }
    }

    /**
     * 显示加载关卡对话框
     */
    async showLoadLevelDialog() {
        const dialog = document.getElementById('load-level-dialog');
        const levelList = document.getElementById('level-list');

        if (!dialog || !levelList) return;

        try {
            // 获取所有关卡
            const levels = await levelManager.getAllLevels();

            if (levels.length === 0) {
                levelList.innerHTML = `
                    <div class="empty-list">
                        <p data-i18n="editor.noLevels">暂无保存的关卡</p>
                    </div>
                `;
            } else {
                let html = '';
                levels.forEach(level => {
                    const date = new Date(level.modifiedAt).toLocaleDateString();
                    html += `
                        <div class="level-item" data-level-id="${level.id}">
                            <div class="level-info">
                                <h4>${level.name}</h4>
                                <p>${level.description || i18nService.t('editor.noDescription')}</p>
                            </div>
                            <div class="level-meta">
                                <div>${i18nService.t('editor.difficulty.' + level.difficulty)}</div>
                                <div>${date}</div>
                            </div>
                        </div>
                    `;
                });
                levelList.innerHTML = html;

                // 绑定点击事件 - 使用触摸友好的事件处理
                levelList.querySelectorAll('.level-item').forEach(item => {
                    this.addTouchFriendlyEvents(item, () => {
                        levelList.querySelectorAll('.level-item').forEach(i => i.classList.remove('selected'));
                        item.classList.add('selected');
                    });
                });
            }

            dialog.classList.remove('hidden');
            i18nService.updatePageText();

        } catch (error) {
            console.error('加载关卡列表失败:', error);
            alert(i18nService.t('editor.loadFailed'));
        }
    }

    /**
     * 隐藏加载关卡对话框
     */
    hideLoadLevelDialog() {
        const dialog = document.getElementById('load-level-dialog');
        if (dialog) {
            dialog.classList.add('hidden');
        }
    }

    /**
     * 加载选中的关卡
     */
    async loadSelectedLevel() {
        const selectedItem = document.querySelector('.level-item.selected');
        if (!selectedItem) {
            alert(i18nService.t('editor.selectLevel'));
            return;
        }

        const levelId = selectedItem.dataset.levelId;
        try {
            const success = await levelEditor.loadLevel(levelId);
            if (success) {
                this.hideLoadLevelDialog();
                this.updateEditorUI();
                console.log('关卡加载成功');
            } else {
                alert(i18nService.t('editor.loadFailed'));
            }
        } catch (error) {
            console.error('加载关卡失败:', error);
            alert(i18nService.t('editor.loadFailed'));
        }
    }

    /**
     * 保存当前关卡
     */
    async saveCurrentLevel() {
        if (!levelEditor.currentLevel) {
            alert(i18nService.t('editor.noLevel'));
            return;
        }

        try {
            const success = await levelEditor.saveLevel();
            if (success) {
                alert(i18nService.t('editor.saveSuccess'));
            } else {
                alert(i18nService.t('editor.saveFailed'));
            }
        } catch (error) {
            console.error('保存关卡失败:', error);
            alert(i18nService.t('editor.saveFailed'));
        }
    }

    /**
     * 测试当前关卡
     */
    async testCurrentLevel() {
        if (!levelEditor.currentLevel) {
            alert(i18nService.t('editor.noLevel'));
            return;
        }

        try {
            await levelEditor.testLevel();
        } catch (error) {
            console.error('测试关卡失败:', error);
            alert(i18nService.t('editor.testFailed'));
        }
    }

    /**
     * 发布当前关卡
     */
    async publishCurrentLevel() {
        if (!levelEditor.currentLevel) {
            alert(i18nService.t('editor.noLevel'));
            return;
        }

        try {
            const success = await levelEditor.publishLevel();
            if (success) {
                this.updateEditorUI();
            }
        } catch (error) {
            console.error('发布关卡失败:', error);
        }
    }

    /**
     * 放大视图
     */
    zoomIn() {
        if (levelEditor.initialized) {
            levelEditor.editorState.zoom = Math.min(5.0, levelEditor.editorState.zoom * 1.2);
            this.updateZoomDisplay();
            levelEditor.render();
        }
    }

    /**
     * 缩小视图
     */
    zoomOut() {
        if (levelEditor.initialized) {
            levelEditor.editorState.zoom = Math.max(0.1, levelEditor.editorState.zoom / 1.2);
            this.updateZoomDisplay();
            levelEditor.render();
        }
    }

    /**
     * 更新缩放显示
     */
    updateZoomDisplay() {
        const zoomLevel = document.getElementById('zoom-level');
        if (zoomLevel && levelEditor.initialized) {
            const percentage = Math.round(levelEditor.editorState.zoom * 100);
            zoomLevel.textContent = percentage + '%';
        }
    }

    /**
     * 更新编辑器UI
     */
    updateEditorUI() {
        if (!levelEditor.initialized || !levelEditor.currentLevel) return;

        // 更新统计信息
        this.updateLevelStats();

        // 更新缩放显示
        this.updateZoomDisplay();

        // 更新国际化文本
        i18nService.updatePageText();
    }

    /**
     * 更新关卡统计信息
     */
    updateLevelStats() {
        if (!levelEditor.currentLevel) return;

        const objects = levelEditor.currentLevel.objects;
        const totalObjects = objects.length;
        const sparkCount = objects.filter(obj => obj.type === 'spark').length;
        const obstacleCount = objects.filter(obj => obj.type === 'obstacle').length;
        const powerupCount = objects.filter(obj => obj.type === 'powerup').length;

        const totalObjectsEl = document.getElementById('total-objects');
        const sparkCountEl = document.getElementById('spark-count');
        const obstacleCountEl = document.getElementById('obstacle-count');
        const powerupCountEl = document.getElementById('powerup-count');

        if (totalObjectsEl) totalObjectsEl.textContent = totalObjects;
        if (sparkCountEl) sparkCountEl.textContent = sparkCount;
        if (obstacleCountEl) obstacleCountEl.textContent = obstacleCount;
        if (powerupCountEl) powerupCountEl.textContent = powerupCount;
    }

    // 自定义关卡相关方法

    /**
     * 加载自定义关卡列表
     */
    async loadCustomLevelsList() {
        const levelsContainer = document.getElementById('levels-list');
        const loadingState = document.getElementById('levels-loading');
        const emptyState = document.getElementById('levels-empty');
        const totalCountEl = document.getElementById('total-levels-count');

        if (!levelsContainer || !levelManager.initialized) return;

        // 显示加载状态
        this.showLoadingState(true);

        try {
            // 获取过滤和排序参数
            const filterBy = document.getElementById('level-filter')?.value || 'published';
            const sortBy = document.getElementById('level-sort')?.value || 'rating';
            const searchTerm = document.getElementById('level-search')?.value || '';

            // 获取关卡列表
            let levels = levelManager.getCustomLevels(sortBy, filterBy);

            // 应用搜索过滤
            if (searchTerm.trim()) {
                levels = levels.filter(level =>
                    level.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    level.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    level.author.toLowerCase().includes(searchTerm.toLowerCase())
                );
            }

            // 更新统计
            if (totalCountEl) {
                totalCountEl.textContent = levels.length;
            }

            // 隐藏加载状态
            this.showLoadingState(false);

            if (levels.length === 0) {
                // 显示空状态
                this.showEmptyState(true);
                levelsContainer.innerHTML = '';
            } else {
                // 显示关卡列表
                this.showEmptyState(false);
                this.renderLevelsList(levels);
            }

        } catch (error) {
            console.error('加载自定义关卡列表失败:', error);
            this.showLoadingState(false);
            this.showEmptyState(true);
        }
    }

    /**
     * 渲染关卡列表
     */
    renderLevelsList(levels) {
        const levelsContainer = document.getElementById('levels-list');
        if (!levelsContainer) return;

        let html = '';
        const currentPlayer = playerManager.getCurrentPlayer();

        levels.forEach(level => {
            const isMyLevel = level.authorId === currentPlayer.id;
            const difficultyClass = `difficulty-${level.difficulty}`;
            const createdDate = new Date(level.createdAt).toLocaleDateString();

            html += `
                <div class="level-card" data-level-id="${level.id}">
                    <div class="level-card-header">
                        <h3 class="level-name">${level.name}</h3>
                        <div class="level-badges">
                            <span class="difficulty-badge ${difficultyClass}" data-i18n="editor.difficulty.${level.difficulty}">${this.getDifficultyText(level.difficulty)}</span>
                            ${isMyLevel ? '<span class="my-level-badge" data-i18n="customLevels.myLevel">我的</span>' : ''}
                        </div>
                    </div>

                    <div class="level-card-body">
                        <p class="level-description">${level.description || i18nService.t('customLevels.noDescription')}</p>

                        <div class="level-meta">
                            <div class="meta-row">
                                <span class="meta-label" data-i18n="customLevels.author">作者:</span>
                                <span class="meta-value">${level.author}</span>
                            </div>
                            <div class="meta-row">
                                <span class="meta-label" data-i18n="customLevels.playCount">游玩:</span>
                                <span class="meta-value">${level.playCount}</span>
                            </div>
                            <div class="meta-row">
                                <span class="meta-label" data-i18n="customLevels.created">创建:</span>
                                <span class="meta-value">${createdDate}</span>
                            </div>
                        </div>

                        <div class="level-rating">
                            <div class="rating-display">
                                <span class="rating-item">
                                    <span class="rating-icon">👍</span>
                                    <span class="rating-count">${level.rating.likes}</span>
                                </span>
                                <span class="rating-item">
                                    <span class="rating-icon">👎</span>
                                    <span class="rating-count">${level.rating.dislikes}</span>
                                </span>
                                <span class="rating-score ${level.rating.totalRating >= 0 ? 'positive' : 'negative'}">
                                    ${level.rating.totalRating >= 0 ? '+' : ''}${level.rating.totalRating}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="level-card-actions">
                        <button class="level-action-btn primary play-btn" data-level-id="${level.id}" data-i18n="customLevels.play">游玩</button>
                        <button class="level-action-btn info-btn" data-level-id="${level.id}" data-i18n="customLevels.details">详情</button>
                        ${isMyLevel ? `<button class="level-action-btn edit-btn" data-level-id="${level.id}" data-i18n="customLevels.edit">编辑</button>` : ''}
                    </div>
                </div>
            `;
        });

        levelsContainer.innerHTML = html;

        // 绑定卡片事件
        this.bindLevelCardEvents();

        // 更新国际化文本
        i18nService.updatePageText();
    }

    /**
     * 绑定关卡卡片事件 - 增强触摸支持
     */
    bindLevelCardEvents() {
        const levelsContainer = document.getElementById('levels-list');
        if (!levelsContainer) return;

        // 游玩按钮
        levelsContainer.querySelectorAll('.play-btn').forEach(btn => {
            this.addTouchFriendlyEvents(btn, (e) => {
                e.stopPropagation();
                const levelId = btn.dataset.levelId;
                this.playLevel(levelId);
            });
        });

        // 详情按钮
        levelsContainer.querySelectorAll('.info-btn').forEach(btn => {
            this.addTouchFriendlyEvents(btn, (e) => {
                e.stopPropagation();
                const levelId = btn.dataset.levelId;
                this.showLevelDetail(levelId);
            });
        });

        // 编辑按钮
        levelsContainer.querySelectorAll('.edit-btn').forEach(btn => {
            this.addTouchFriendlyEvents(btn, (e) => {
                e.stopPropagation();
                const levelId = btn.dataset.levelId;
                this.editLevel(levelId);
            });
        });

        // 卡片点击事件（显示详情）
        levelsContainer.querySelectorAll('.level-card').forEach(card => {
            this.addTouchFriendlyEvents(card, (e) => {
                // 如果点击的是按钮，不处理卡片点击
                if (e.target.closest('.level-action-btn')) {
                    return;
                }

                const levelId = card.dataset.levelId;
                if (levelId) {
                    this.showLevelDetail(levelId);
                }
            });
        });
    }

    /**
     * 绑定自定义关卡控制事件
     */
    bindCustomLevelsControls() {
        // 过滤器变化
        const filterSelect = document.getElementById('level-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', () => {
                this.loadCustomLevelsList();
            });
        }

        // 排序变化
        const sortSelect = document.getElementById('level-sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', () => {
                this.loadCustomLevelsList();
            });
        }

        // 搜索
        const searchInput = document.getElementById('level-search');
        const searchBtn = document.getElementById('search-btn');

        if (searchInput) {
            // 实时搜索（防抖）
            let searchTimeout;
            searchInput.addEventListener('input', () => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.loadCustomLevelsList();
                }, 500);
            });

            // 回车搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.loadCustomLevelsList();
                }
            });
        }

        if (searchBtn) {
            this.addTouchFriendlyEvents(searchBtn, () => {
                this.loadCustomLevelsList();
            });
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoadingState(show) {
        const loadingState = document.getElementById('levels-loading');
        if (loadingState) {
            if (show) {
                loadingState.classList.remove('hidden');
            } else {
                loadingState.classList.add('hidden');
            }
        }
    }

    /**
     * 显示/隐藏空状态
     */
    showEmptyState(show) {
        const emptyState = document.getElementById('levels-empty');
        if (emptyState) {
            if (show) {
                emptyState.classList.remove('hidden');
            } else {
                emptyState.classList.add('hidden');
            }
        }
    }

    /**
     * 获取难度文本
     */
    getDifficultyText(difficulty) {
        const difficultyMap = {
            'easy': '简单',
            'normal': '普通',
            'hard': '困难',
            'expert': '专家'
        };
        return difficultyMap[difficulty] || '普通';
    }

    /**
     * 显示关卡详情
     */
    async showLevelDetail(levelId) {
        const level = levelManager.getLevel(levelId);
        if (!level) {
            console.error('关卡不存在:', levelId);
            return;
        }

        // 更新详情对话框内容
        this.updateLevelDetailDialog(level);

        // 渲染关卡预览
        this.renderLevelPreview(level);

        // 显示对话框
        this.showOverlay('level-detail-dialog');

        // 保存当前选中的关卡ID
        this.selectedLevelId = levelId;

        // 更新国际化文本
        i18nService.updatePageText();
    }

    /**
     * 更新关卡详情对话框
     */
    updateLevelDetailDialog(level) {
        const currentPlayer = playerManager.getCurrentPlayer();
        const isMyLevel = level.authorId === currentPlayer.id;
        const createdDate = new Date(level.createdAt).toLocaleDateString();
        const playerRating = levelManager.getPlayerRating(level.id);

        // 基本信息
        document.getElementById('level-detail-name').textContent = level.name;
        document.getElementById('level-detail-author').textContent = level.author;
        document.getElementById('level-detail-difficulty').textContent = this.getDifficultyText(level.difficulty);
        document.getElementById('level-detail-play-count').textContent = level.playCount;
        document.getElementById('level-detail-created').textContent = createdDate;
        document.getElementById('level-detail-description').textContent = level.description || i18nService.t('customLevels.noDescription');

        // 评分信息
        document.getElementById('level-detail-likes').textContent = level.rating.likes;
        document.getElementById('level-detail-dislikes').textContent = level.rating.dislikes;
        document.getElementById('level-detail-rating-score').textContent = level.rating.totalRating;

        // 设置难度徽章样式
        const difficultyEl = document.getElementById('level-detail-difficulty');
        difficultyEl.className = `difficulty-badge difficulty-${level.difficulty}`;

        // 更新评分按钮状态
        const likeBtn = document.getElementById('like-level-btn');
        const dislikeBtn = document.getElementById('dislike-level-btn');

        if (currentPlayer.isGuest) {
            // 游客不能评分
            likeBtn.disabled = true;
            dislikeBtn.disabled = true;
            likeBtn.textContent = i18nService.t('customLevels.loginToRate');
            dislikeBtn.textContent = i18nService.t('customLevels.loginToRate');
        } else {
            likeBtn.disabled = false;
            dislikeBtn.disabled = false;

            // 根据用户评分状态更新按钮
            likeBtn.classList.toggle('active', playerRating === true);
            dislikeBtn.classList.toggle('active', playerRating === false);

            likeBtn.textContent = playerRating === true ? i18nService.t('customLevels.liked') : i18nService.t('customLevels.like');
            dislikeBtn.textContent = playerRating === false ? i18nService.t('customLevels.disliked') : i18nService.t('customLevels.dislike');
        }

        // 控制按钮显示
        const editBtn = document.getElementById('edit-level-btn');
        const deleteBtn = document.getElementById('delete-level-btn');

        if (isMyLevel) {
            editBtn.style.display = 'inline-block';
            deleteBtn.style.display = 'inline-block';
        } else {
            editBtn.style.display = 'none';
            deleteBtn.style.display = 'none';
        }
    }

    /**
     * 渲染关卡预览
     */
    renderLevelPreview(level) {
        const canvas = document.getElementById('level-preview-canvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        // 清空画布
        ctx.fillStyle = level.data?.backgroundColor || '#1a1a2e';
        ctx.fillRect(0, 0, width, height);

        // 如果有关卡对象数据，渲染预览
        if (level.objects && level.objects.length > 0) {
            this.renderLevelObjects(ctx, level.objects, width, height);
        } else if (level.data && level.data.sparks) {
            // 兼容旧格式
            this.renderLevelSparks(ctx, level.data.sparks, width, height);
        } else {
            // 显示空关卡提示
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(i18nService.t('customLevels.emptyLevel'), width / 2, height / 2);
        }
    }

    /**
     * 渲染关卡对象
     */
    renderLevelObjects(ctx, objects, canvasWidth, canvasHeight) {
        // 计算缩放比例
        const maxX = Math.max(...objects.map(obj => obj.x), canvasWidth);
        const maxY = Math.max(...objects.map(obj => obj.y), canvasHeight);
        const scaleX = canvasWidth / Math.max(maxX, canvasWidth);
        const scaleY = canvasHeight / Math.max(maxY, canvasHeight);
        const scale = Math.min(scaleX, scaleY, 1);

        objects.forEach(obj => {
            const x = obj.x * scale;
            const y = obj.y * scale;

            ctx.save();

            switch (obj.type) {
                case 'spark':
                    this.drawSparkPreview(ctx, x, y, obj.subType || 'normal');
                    break;
                case 'obstacle':
                    this.drawObstaclePreview(ctx, x, y, obj.subType || 'wall');
                    break;
                case 'powerup':
                    this.drawPowerupPreview(ctx, x, y, obj.subType || 'timeSlowdown');
                    break;
                case 'trigger':
                    this.drawTriggerPreview(ctx, x, y);
                    break;
            }

            ctx.restore();
        });
    }

    /**
     * 渲染关卡光点（兼容旧格式）
     */
    renderLevelSparks(ctx, sparks, canvasWidth, canvasHeight) {
        if (!sparks || sparks.length === 0) return;

        sparks.forEach(spark => {
            const x = (spark.x / 800) * canvasWidth; // 假设原始画布宽度为800
            const y = (spark.y / 600) * canvasHeight; // 假设原始画布高度为600

            this.drawSparkPreview(ctx, x, y, spark.type || 'normal');
        });
    }

    /**
     * 绘制光点预览
     */
    drawSparkPreview(ctx, x, y, type) {
        const colors = {
            'normal': '#ffff00',
            'fast': '#ff6600',
            'slow': '#0066ff',
            'bonus': '#ff00ff',
            'perfect': '#00ffff'
        };

        ctx.fillStyle = colors[type] || colors.normal;
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fill();
    }

    /**
     * 绘制障碍物预览
     */
    drawObstaclePreview(ctx, x, y, type) {
        ctx.fillStyle = '#ff0000';

        switch (type) {
            case 'wall':
                ctx.fillRect(x - 5, y - 5, 10, 10);
                break;
            case 'movingWall':
                ctx.fillRect(x - 5, y - 5, 10, 10);
                ctx.strokeStyle = '#ff6666';
                ctx.strokeRect(x - 6, y - 6, 12, 12);
                break;
            case 'spike':
                ctx.beginPath();
                ctx.moveTo(x, y - 5);
                ctx.lineTo(x - 4, y + 3);
                ctx.lineTo(x + 4, y + 3);
                ctx.closePath();
                ctx.fill();
                break;
        }
    }

    /**
     * 绘制道具预览
     */
    drawPowerupPreview(ctx, x, y, type) {
        const colors = {
            'timeSlowdown': '#00ff00',
            'doubleScore': '#ffff00',
            'shield': '#0000ff',
            'multiHit': '#ff00ff'
        };

        ctx.fillStyle = colors[type] || colors.timeSlowdown;
        ctx.fillRect(x - 4, y - 4, 8, 8);
    }

    /**
     * 绘制触发器预览
     */
    drawTriggerPreview(ctx, x, y) {
        ctx.strokeStyle = '#888888';
        ctx.setLineDash([2, 2]);
        ctx.strokeRect(x - 6, y - 6, 12, 12);
        ctx.setLineDash([]);
    }

    /**
     * 游玩关卡
     */
    async playLevel(levelId) {
        const level = levelManager.getLevel(levelId);
        if (!level) {
            console.error('关卡不存在:', levelId);
            return;
        }

        try {
            // 增加游玩次数
            await levelManager.incrementPlayCount(levelId);

            // 加载自定义关卡到游戏引擎
            const success = await gameEngine.loadCustomLevel(level);
            if (success) {
                // 切换到游戏界面
                this.showScreen('game-screen');

                // 开始游戏
                gameEngine.startGame();

                console.log('开始游玩自定义关卡:', level.name);
            } else {
                alert(i18nService.t('customLevels.loadFailed'));
            }

        } catch (error) {
            console.error('游玩关卡失败:', error);
            alert(i18nService.t('customLevels.playFailed'));
        }
    }

    /**
     * 编辑关卡
     */
    async editLevel(levelId) {
        const level = levelManager.getLevel(levelId);
        if (!level) {
            console.error('关卡不存在:', levelId);
            return;
        }

        const currentPlayer = playerManager.getCurrentPlayer();
        if (level.authorId !== currentPlayer.id && !currentPlayer.isGuest) {
            alert(i18nService.t('customLevels.noPermission'));
            return;
        }

        try {
            // 切换到关卡编辑器
            await this.showLevelEditor();

            // 加载关卡到编辑器
            const success = await levelEditor.loadLevelData(level);
            if (success) {
                console.log('开始编辑关卡:', level.name);
            } else {
                alert(i18nService.t('editor.loadFailed'));
            }

        } catch (error) {
            console.error('编辑关卡失败:', error);
            alert(i18nService.t('editor.loadFailed'));
        }
    }

    /**
     * 删除关卡
     */
    async deleteLevel(levelId) {
        const level = levelManager.getLevel(levelId);
        if (!level) {
            console.error('关卡不存在:', levelId);
            return;
        }

        const currentPlayer = playerManager.getCurrentPlayer();
        if (level.authorId !== currentPlayer.id && !currentPlayer.isGuest) {
            alert(i18nService.t('customLevels.noPermission'));
            return;
        }

        // 确认删除
        const confirmed = confirm(i18nService.t('customLevels.confirmDelete', { name: level.name }));
        if (!confirmed) return;

        try {
            const success = await levelManager.deleteCustomLevel(levelId);
            if (success) {
                // 关闭详情对话框
                this.hideOverlay('level-detail-dialog');

                // 刷新关卡列表
                await this.loadCustomLevelsList();

                alert(i18nService.t('customLevels.deleteSuccess'));
                console.log('删除关卡成功:', level.name);
            } else {
                alert(i18nService.t('customLevels.deleteFailed'));
            }

        } catch (error) {
            console.error('删除关卡失败:', error);
            alert(i18nService.t('customLevels.deleteFailed'));
        }
    }

    /**
     * 从详情对话框游玩选中的关卡
     */
    playSelectedLevel() {
        if (this.selectedLevelId) {
            this.hideOverlay('level-detail-dialog');
            this.playLevel(this.selectedLevelId);
        }
    }

    /**
     * 从详情对话框编辑选中的关卡
     */
    editSelectedLevel() {
        if (this.selectedLevelId) {
            this.hideOverlay('level-detail-dialog');
            this.editLevel(this.selectedLevelId);
        }
    }

    /**
     * 从详情对话框删除选中的关卡
     */
    deleteSelectedLevel() {
        if (this.selectedLevelId) {
            this.deleteLevel(this.selectedLevelId);
        }
    }

    /**
     * 对关卡进行评分
     */
    async rateLevelLevel(isLike) {
        if (!this.selectedLevelId) return;

        const currentPlayer = playerManager.getCurrentPlayer();
        if (currentPlayer.isGuest) {
            alert(i18nService.t('customLevels.loginToRate'));
            return;
        }

        try {
            const success = await levelManager.rateLevelLevel(this.selectedLevelId, isLike);
            if (success) {
                // 刷新详情对话框
                const level = levelManager.getLevel(this.selectedLevelId);
                if (level) {
                    this.updateLevelDetailDialog(level);
                }

                // 刷新关卡列表
                await this.loadCustomLevelsList();

                console.log('评分成功:', isLike ? '点赞' : '踩');
            } else {
                alert(i18nService.t('customLevels.rateFailed'));
            }

        } catch (error) {
            console.error('评分失败:', error);
            alert(i18nService.t('customLevels.rateFailed'));
        }
    }
}

// 创建全局屏幕管理器实例
// 修复：延迟到DOM准备好之后再创建实例，避免初始化时机问题
if (document.readyState === 'loading') {
    // DOM还在加载中，等待DOMContentLoaded事件
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📱 DOM已准备好，创建ScreenManager实例...');
        window.screenManager = new ScreenManager();
        console.log('✅ ScreenManager实例已创建并设置为全局变量');
    });
} else {
    // DOM已经准备好，直接创建实例
    console.log('📱 DOM已就绪，直接创建ScreenManager实例...');
    window.screenManager = new ScreenManager();
    console.log('✅ ScreenManager实例已创建并设置为全局变量');
}
