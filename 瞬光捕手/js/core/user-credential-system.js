/**
 * 瞬光捕手 - 云存储用户凭证系统
 * 提供用户友好的唯一标识符，支持多设备同步和冲突检测
 */

class UserCredentialSystem {
    constructor() {
        this.initialized = false;
        this.currentCredential = null;
        
        // 凭证配置
        this.config = {
            // 用户名规则
            username: {
                minLength: 3,
                maxLength: 16,
                allowedChars: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, // 字母、数字、下划线、中文
                reservedNames: ['admin', 'system', 'guest', 'anonymous', 'test']
            },
            
            // 随机后缀配置
            suffix: {
                length: 4,
                chars: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
            },
            
            // 设备指纹配置
            deviceFingerprint: {
                enabled: true,
                factors: ['screen', 'timezone', 'language', 'platform', 'memory']
            }
        };
        
        // 凭证类型
        this.credentialTypes = {
            USERNAME_SUFFIX: 'username_suffix',    // 用户名+随机后缀
            EMAIL_BASED: 'email_based',           // 邮箱验证
            DEVICE_FINGERPRINT: 'device_fingerprint', // 设备指纹
            ANONYMOUS: 'anonymous'                // 匿名模式
        };
        
        console.log('🔐 用户凭证系统已创建');
    }

    /**
     * 安全初始化凭证系统 - 不自动加载凭证
     */
    async init() {
        try {
            // 不自动加载现有凭证，等待用户主动认证
            console.log('🔐 用户凭证系统已就绪，等待用户主动认证');

            // 清除可能存在的自动凭证
            await this.clearStoredCredentials();

            this.initialized = true;
            console.log('✅ 用户凭证系统安全初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 用户凭证系统初始化失败:', error);
            this.initialized = true; // 即使失败也标记为已初始化
            return false;
        }
    }

    /**
     * 清除存储的凭证 - 防止自动登录
     */
    async clearStoredCredentials() {
        try {
            if (window.storageService) {
                // 清除用户凭证
                await storageService.delete('user.credential');
                console.log('🧹 已清除存储的用户凭证');
            }

            // 清除本地存储中的相关数据
            if (typeof localStorage !== 'undefined') {
                localStorage.removeItem('user_credential');
                localStorage.removeItem('device_fingerprint');
                console.log('🧹 已清除本地存储的凭证数据');
            }

            this.currentCredential = null;
            return true;
        } catch (error) {
            console.warn('⚠️ 清除存储凭证时出现错误:', error);
            return false;
        }
    }

    /**
     * 安全的用户认证 - 需要用户明确同意
     * @param {Object} authRequest - 认证请求
     * @param {boolean} userConsent - 用户是否明确同意
     * @returns {Promise<Object|null>} 认证结果
     */
    async secureAuthenticate(authRequest, userConsent = false) {
        if (!userConsent) {
            console.log('🔐 用户认证需要明确同意');
            return null;
        }

        if (!authRequest || !authRequest.type) {
            console.log('🔐 无效的认证请求');
            return null;
        }

        try {
            let credential = null;

            switch (authRequest.type) {
                case this.credentialTypes.USERNAME_SUFFIX:
                    if (authRequest.username) {
                        credential = await this.createUsernameSuffixCredential(authRequest.username);
                    }
                    break;
                case this.credentialTypes.EMAIL_BASED:
                    if (authRequest.email) {
                        credential = await this.createEmailBasedCredential(authRequest.email);
                    }
                    break;
                case this.credentialTypes.DEVICE_FINGERPRINT:
                    credential = await this.createDeviceFingerprintCredential();
                    break;
                case this.credentialTypes.ANONYMOUS:
                    credential = await this.createAnonymousCredential();
                    break;
                default:
                    console.log('🔐 不支持的认证类型:', authRequest.type);
                    return null;
            }

            if (credential) {
                this.currentCredential = credential;
                console.log(`✅ 用户认证成功: ${credential.displayName} (${credential.type})`);
                return credential;
            }

            return null;
        } catch (error) {
            console.error('❌ 用户认证失败:', error);
            return null;
        }
    }

    /**
     * 创建用户名+后缀类型凭证
     * @param {string} username - 用户名
     * @returns {Promise<Object>} 凭证对象
     */
    async createUsernameSuffixCredential(username) {
        // 验证用户名
        const validation = this.validateUsername(username);
        if (!validation.valid) {
            throw new Error(`用户名无效: ${validation.reason}`);
        }

        // 检查用户名是否已被使用
        const isAvailable = await this.checkUsernameAvailability(username);
        if (!isAvailable.available) {
            throw new Error(`用户名不可用: ${isAvailable.reason}`);
        }

        // 生成随机后缀
        const suffix = this.generateRandomSuffix();
        const fullIdentifier = `${username}_${suffix}`; // 使用下划线代替井号，符合存储键验证规则

        // 创建凭证对象
        const credential = {
            type: this.credentialTypes.USERNAME_SUFFIX,
            identifier: fullIdentifier,
            username: username,
            suffix: suffix,
            displayName: username,
            fullDisplayName: fullIdentifier,
            createdAt: Date.now(),
            deviceInfo: this.getDeviceInfo(),
            verified: false
        };

        // 检查凭证冲突
        const conflictCheck = await this.checkCredentialConflict(credential);
        if (conflictCheck.hasConflict) {
            // 如果有冲突，重新生成后缀
            return await this.createUsernameSuffixCredential(username);
        }

        // 保存凭证
        await this.saveCredential(credential);
        
        console.log(`✅ 创建用户名凭证: ${fullIdentifier}`);
        return credential;
    }

    /**
     * 创建邮箱验证类型凭证
     * @param {string} email - 邮箱地址
     * @returns {Promise<Object>} 凭证对象
     */
    async createEmailBasedCredential(email) {
        // 验证邮箱格式
        if (!this.validateEmail(email)) {
            throw new Error('邮箱格式无效');
        }

        // 检查邮箱是否已被使用
        const isAvailable = await this.checkEmailAvailability(email);
        if (!isAvailable.available) {
            throw new Error(`邮箱已被使用: ${isAvailable.reason}`);
        }

        // 生成验证码
        const verificationCode = this.generateVerificationCode();

        // 创建凭证对象
        const credential = {
            type: this.credentialTypes.EMAIL_BASED,
            identifier: email,
            email: email,
            displayName: email.split('@')[0],
            fullDisplayName: email,
            verificationCode: verificationCode,
            verified: false,
            createdAt: Date.now(),
            deviceInfo: this.getDeviceInfo()
        };

        // 发送验证邮件（模拟）
        await this.sendVerificationEmail(email, verificationCode);

        // 保存凭证
        await this.saveCredential(credential);
        
        console.log(`✅ 创建邮箱凭证: ${email} (待验证)`);
        return credential;
    }

    /**
     * 创建设备指纹类型凭证
     * @returns {Promise<Object>} 凭证对象
     */
    async createDeviceFingerprintCredential() {
        // 生成设备指纹
        const fingerprint = await this.generateDeviceFingerprint();
        
        // 生成友好的显示名称
        const displayName = this.generateFriendlyDeviceName();

        // 创建凭证对象
        const credential = {
            type: this.credentialTypes.DEVICE_FINGERPRINT,
            identifier: fingerprint,
            fingerprint: fingerprint,
            displayName: displayName,
            fullDisplayName: `${displayName} (${fingerprint.substring(0, 8)})`,
            createdAt: Date.now(),
            deviceInfo: this.getDeviceInfo(),
            verified: true // 设备指纹自动验证
        };

        // 检查指纹冲突
        const conflictCheck = await this.checkCredentialConflict(credential);
        if (conflictCheck.hasConflict) {
            // 设备指纹冲突很少见，但如果发生，添加时间戳
            credential.identifier += '_' + Date.now();
            credential.fullDisplayName += ` (${Date.now()})`;
        }

        // 保存凭证
        await this.saveCredential(credential);
        
        console.log(`✅ 创建设备指纹凭证: ${displayName}`);
        return credential;
    }

    /**
     * 创建匿名凭证 - 仅会话有效，不持久化
     * @returns {Promise<Object>} 凭证对象
     */
    async createAnonymousCredential() {
        const anonymousId = 'anon_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const displayName = '匿名玩家' + Math.floor(Math.random() * 10000);

        const credential = {
            type: this.credentialTypes.ANONYMOUS,
            identifier: anonymousId,
            displayName: displayName,
            fullDisplayName: displayName,
            createdAt: Date.now(),
            deviceInfo: this.getDeviceInfo(),
            verified: true,
            temporary: true, // 标记为临时凭证
            sessionOnly: true // 仅会话有效，不持久化
        };

        // 匿名凭证不保存到存储，仅在内存中保持
        // await this.saveCredential(credential);

        console.log(`✅ 创建匿名凭证（仅会话有效）: ${displayName}`);
        return credential;
    }

    /**
     * 验证用户名
     * @param {string} username - 用户名
     * @returns {Object} 验证结果
     */
    validateUsername(username) {
        const config = this.config.username;
        
        if (!username || typeof username !== 'string') {
            return { valid: false, reason: '用户名不能为空' };
        }
        
        if (username.length < config.minLength) {
            return { valid: false, reason: `用户名长度不能少于${config.minLength}个字符` };
        }
        
        if (username.length > config.maxLength) {
            return { valid: false, reason: `用户名长度不能超过${config.maxLength}个字符` };
        }
        
        if (!config.allowedChars.test(username)) {
            return { valid: false, reason: '用户名只能包含字母、数字、下划线和中文字符' };
        }
        
        if (config.reservedNames.includes(username.toLowerCase())) {
            return { valid: false, reason: '该用户名为系统保留名称' };
        }
        
        return { valid: true };
    }

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 检查用户名可用性
     * @param {string} username - 用户名
     * @returns {Promise<Object>} 可用性检查结果
     */
    async checkUsernameAvailability(username) {
        try {
            // 这里应该调用云存储API检查用户名是否已存在
            // 模拟API调用
            const response = await this.mockApiCall('checkUsername', { username });
            
            return {
                available: response.available,
                reason: response.reason || ''
            };
        } catch (error) {
            console.warn('⚠️ 用户名可用性检查失败，假设可用:', error);
            return { available: true };
        }
    }

    /**
     * 检查邮箱可用性
     * @param {string} email - 邮箱地址
     * @returns {Promise<Object>} 可用性检查结果
     */
    async checkEmailAvailability(email) {
        try {
            // 这里应该调用云存储API检查邮箱是否已存在
            const response = await this.mockApiCall('checkEmail', { email });
            
            return {
                available: response.available,
                reason: response.reason || ''
            };
        } catch (error) {
            console.warn('⚠️ 邮箱可用性检查失败，假设可用:', error);
            return { available: true };
        }
    }

    /**
     * 检查凭证冲突
     * @param {Object} credential - 凭证对象
     * @returns {Promise<Object>} 冲突检查结果
     */
    async checkCredentialConflict(credential) {
        try {
            const response = await this.mockApiCall('checkCredentialConflict', {
                identifier: credential.identifier,
                type: credential.type
            });
            
            return {
                hasConflict: response.hasConflict,
                conflictInfo: response.conflictInfo || null
            };
        } catch (error) {
            console.warn('⚠️ 凭证冲突检查失败，假设无冲突:', error);
            return { hasConflict: false };
        }
    }

    /**
     * 生成随机后缀
     * @returns {string} 随机后缀
     */
    generateRandomSuffix() {
        const config = this.config.suffix;
        let suffix = '';
        
        for (let i = 0; i < config.length; i++) {
            suffix += config.chars.charAt(Math.floor(Math.random() * config.chars.length));
        }
        
        return suffix;
    }

    /**
     * 生成验证码
     * @returns {string} 6位数字验证码
     */
    generateVerificationCode() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    /**
     * 生成设备指纹
     * @returns {Promise<string>} 设备指纹
     */
    async generateDeviceFingerprint() {
        const factors = [];
        
        // 屏幕信息
        factors.push(`${screen.width}x${screen.height}x${screen.colorDepth}`);
        
        // 时区
        factors.push(Intl.DateTimeFormat().resolvedOptions().timeZone);
        
        // 语言
        factors.push(navigator.language);
        
        // 平台
        factors.push(navigator.platform);
        
        // 内存信息（如果可用）
        if (navigator.deviceMemory) {
            factors.push(navigator.deviceMemory.toString());
        }
        
        // 用户代理（部分）
        factors.push(navigator.userAgent.substring(0, 50));
        
        // 生成哈希
        const fingerprint = await this.hashString(factors.join('|'));
        return fingerprint.substring(0, 16); // 取前16位
    }

    /**
     * 生成友好的设备名称
     * @returns {string} 设备名称
     */
    generateFriendlyDeviceName() {
        const deviceTypes = ['电脑', '手机', '平板'];
        const adjectives = ['快速', '智能', '强大', '便携', '高效'];
        const colors = ['蓝色', '红色', '绿色', '金色', '银色'];
        
        const deviceType = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
        const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
        const color = colors[Math.floor(Math.random() * colors.length)];
        
        return `${adjective}的${color}${deviceType}`;
    }

    /**
     * 获取设备信息
     * @returns {Object} 设备信息
     */
    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timestamp: Date.now()
        };
    }

    /**
     * 字符串哈希函数
     * @param {string} str - 输入字符串
     * @returns {Promise<string>} 哈希值
     */
    async hashString(str) {
        if (crypto && crypto.subtle) {
            const encoder = new TextEncoder();
            const data = encoder.encode(str);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        } else {
            // 简单哈希函数作为回退
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return Math.abs(hash).toString(16);
        }
    }

    /**
     * 模拟API调用
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>} 响应数据
     */
    async mockApiCall(endpoint, data) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
        
        switch (endpoint) {
            case 'checkUsername':
                // 模拟用户名检查
                const reservedUsernames = ['admin', 'test', 'user', 'player'];
                return {
                    available: !reservedUsernames.includes(data.username.toLowerCase()),
                    reason: reservedUsernames.includes(data.username.toLowerCase()) ? '用户名已被使用' : ''
                };
                
            case 'checkEmail':
                // 模拟邮箱检查
                return {
                    available: !data.email.includes('test@'),
                    reason: data.email.includes('test@') ? '邮箱已被使用' : ''
                };
                
            case 'checkCredentialConflict':
                // 模拟凭证冲突检查
                return {
                    hasConflict: Math.random() < 0.05, // 5%的冲突概率
                    conflictInfo: null
                };
                
            default:
                throw new Error(`未知的API端点: ${endpoint}`);
        }
    }

    /**
     * 发送验证邮件（模拟）
     * @param {string} email - 邮箱地址
     * @param {string} code - 验证码
     */
    async sendVerificationEmail(email, code) {
        console.log(`📧 发送验证邮件到 ${email}，验证码: ${code}`);
        // 在实际实现中，这里应该调用邮件服务API
    }

    /**
     * 验证邮箱验证码
     * @param {string} code - 验证码
     * @returns {Promise<boolean>} 验证是否成功
     */
    async verifyEmailCode(code) {
        if (!this.currentCredential || this.currentCredential.type !== this.credentialTypes.EMAIL_BASED) {
            throw new Error('当前凭证不是邮箱类型或不存在');
        }
        
        if (this.currentCredential.verificationCode === code) {
            this.currentCredential.verified = true;
            this.currentCredential.verifiedAt = Date.now();
            await this.saveCredential(this.currentCredential);
            
            console.log('✅ 邮箱验证成功');
            return true;
        } else {
            console.log('❌ 验证码错误');
            return false;
        }
    }

    /**
     * 保存凭证
     * @param {Object} credential - 凭证对象
     */
    async saveCredential(credential) {
        this.currentCredential = credential;

        if (window.storageService) {
            console.log('💾 保存用户凭证到存储服务...');
            try {
                await window.storageService.set('user.credential', credential);
                console.log('✅ 用户凭证保存成功');
            } catch (error) {
                console.error('❌ 用户凭证保存失败:', error);
                throw error;
            }
        } else {
            console.warn('⚠️ 存储服务不可用，凭证仅保存在内存中');
        }

        // 触发凭证变更事件
        this.dispatchCredentialChangeEvent(credential);
    }

    /**
     * 验证凭证
     * @param {Object} credential - 凭证对象
     * @returns {boolean} 是否有效
     */
    validateCredential(credential) {
        if (!credential || typeof credential !== 'object') {
            return false;
        }
        
        const requiredFields = ['type', 'identifier', 'displayName', 'createdAt'];
        return requiredFields.every(field => credential.hasOwnProperty(field));
    }

    /**
     * 获取当前凭证
     * @returns {Object|null} 当前凭证
     */
    getCurrentCredential() {
        return this.currentCredential;
    }

    /**
     * 获取凭证显示信息
     * @returns {Object} 显示信息
     */
    getCredentialDisplayInfo() {
        if (!this.currentCredential) {
            return {
                displayName: '未登录',
                fullDisplayName: '未登录用户',
                type: 'none',
                verified: false
            };
        }
        
        return {
            displayName: this.currentCredential.displayName,
            fullDisplayName: this.currentCredential.fullDisplayName,
            type: this.currentCredential.type,
            verified: this.currentCredential.verified || false,
            createdAt: this.currentCredential.createdAt
        };
    }

    /**
     * 注销当前凭证
     */
    async logout() {
        if (this.currentCredential) {
            console.log(`🔐 注销凭证: ${this.currentCredential.displayName}`);
            
            const oldCredential = this.currentCredential;
            this.currentCredential = null;
            
            if (window.storageService) {
                await storageService.remove('user.credential');
            }
            
            // 触发注销事件
            this.dispatchCredentialChangeEvent(null, oldCredential);
        }
    }

    /**
     * 触发凭证变更事件
     * @param {Object} newCredential - 新凭证
     * @param {Object} oldCredential - 旧凭证
     */
    dispatchCredentialChangeEvent(newCredential, oldCredential = null) {
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('credentialChanged', {
                detail: {
                    newCredential,
                    oldCredential,
                    timestamp: Date.now()
                }
            });
            window.dispatchEvent(event);
        }
    }

    /**
     * 获取支持的凭证类型
     * @returns {Array} 凭证类型列表
     */
    getSupportedCredentialTypes() {
        return [
            {
                type: this.credentialTypes.USERNAME_SUFFIX,
                name: '用户名登录',
                description: '使用用户名+随机后缀，易于记忆',
                icon: '👤',
                recommended: true
            },
            {
                type: this.credentialTypes.EMAIL_BASED,
                name: '邮箱登录',
                description: '使用邮箱地址，支持找回',
                icon: '📧',
                recommended: false
            },
            {
                type: this.credentialTypes.DEVICE_FINGERPRINT,
                name: '设备登录',
                description: '基于设备特征，自动识别',
                icon: '📱',
                recommended: false
            },
            {
                type: this.credentialTypes.ANONYMOUS,
                name: '匿名游戏',
                description: '临时游戏，不保存到云端',
                icon: '👻',
                recommended: false
            }
        ];
    }
}

// 创建全局用户凭证系统实例
const userCredentialSystem = new UserCredentialSystem();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.UserCredentialSystem = UserCredentialSystem;
    window.userCredentialSystem = userCredentialSystem;
}

// 支持CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UserCredentialSystem,
        userCredentialSystem
    };
}
