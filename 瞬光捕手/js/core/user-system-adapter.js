/**
 * 瞬光捕手 - 用户系统适配器
 * 桥接新的用户管理系统和现有的PlayerManager、UserCredentialSystem
 * 确保向后兼容性和平滑迁移
 */

class UserSystemAdapter {
    constructor() {
        this.initialized = false;
        this.userManager = null;
        this.userStorageService = null;
        this.playerManager = null;
        this.userCredentialSystem = null;
        this.migrationCompleted = false;
        
        console.log('🔗 用户系统适配器已创建');
    }

    /**
     * 初始化适配器
     */
    async init() {
        try {
            console.log('🔧 初始化用户系统适配器...');
            
            // 确保依赖的服务已初始化
            await this.ensureDependencies();
            
            // 创建用户存储服务
            this.userStorageService = new UserStorageService(window.storageService);
            await this.userStorageService.init();

            // 创建新的用户管理器，传递存储服务
            this.userManager = new UserManager(window.storageService);

            await this.userManager.init();
            
            // 获取现有系统的引用
            this.playerManager = window.playerManager;
            this.userCredentialSystem = window.userCredentialSystem;
            
            // 执行数据迁移
            await this.performDataMigration();
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.initialized = true;
            console.log('✅ 用户系统适配器初始化完成');
            
        } catch (error) {
            console.error('❌ 用户系统适配器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 确保依赖服务已初始化
     */
    async ensureDependencies() {
        const dependencies = [
            { name: 'storageService', instance: window.storageService },
            { name: 'UserDataStructure', instance: window.UserDataStructure },
            { name: 'StorageKeySchema', instance: window.StorageKeySchema },
            { name: 'UserDataValidator', instance: window.UserDataValidator }
        ];

        for (const dep of dependencies) {
            if (!dep.instance) {
                throw new Error(`依赖服务未找到: ${dep.name}`);
            }
        }

        // 特别检查存储服务的初始化状态
        if (window.storageService && !window.storageService.initialized) {
            console.log('⏳ 等待存储服务完成初始化...');

            let retryCount = 0;
            const maxRetries = 100; // 最多等待10秒

            while (!window.storageService.initialized && retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retryCount++;
            }

            if (!window.storageService.initialized) {
                throw new Error('存储服务初始化超时');
            }

            console.log('✅ 存储服务初始化完成');
        }
    }

    /**
     * 执行数据迁移
     */
    async performDataMigration() {
        try {
            // 检查是否已经迁移过
            const migrationStatus = await window.storageService.get(StorageKeySchema.SYSTEM_KEYS.MIGRATION_STATUS);
            if (migrationStatus && migrationStatus.completed) {
                console.log('📋 数据迁移已完成，跳过迁移过程');
                this.migrationCompleted = true;
                return;
            }
            
            console.log('🔄 开始数据迁移...');
            
            // 迁移现有玩家数据
            await this.migratePlayerData();
            
            // 迁移用户凭证数据
            await this.migrateCredentialData();
            
            // 标记迁移完成
            await window.storageService.put(StorageKeySchema.SYSTEM_KEYS.MIGRATION_STATUS, {
                completed: true,
                completedAt: Date.now(),
                version: '1.0'
            });
            
            this.migrationCompleted = true;
            console.log('✅ 数据迁移完成');
            
        } catch (error) {
            console.error('❌ 数据迁移失败:', error);
            // 即使迁移失败，也继续运行，但记录错误
        }
    }

    /**
     * 迁移现有玩家数据
     */
    async migratePlayerData() {
        try {
            // 获取所有玩家数据键
            const playerKeys = await window.storageService.list('player.');
            
            for (const key of playerKeys) {
                if (key.endsWith('.profile')) {
                    const playerData = await window.storageService.get(key);
                    if (playerData && playerData.id) {
                        // 创建新用户（使用玩家ID作为标识符，玩家名称作为显示名称）
                        const identifier = playerData.id;
                        const displayName = playerData.name || `玩家${playerData.id}`;
                        
                        // 检查用户是否已存在
                        if (!await this.userManager.userExists(identifier)) {
                            // 创建用户核心数据
                            const userCore = {
                                ...UserDataStructure.getUserCoreStructure(),
                                identifier: identifier,
                                displayName: displayName,
                                type: playerData.isGuest ? 'guest' : 'registered',
                                createdAt: playerData.createdAt || Date.now(),
                                lastActiveAt: playerData.lastPlayedAt || Date.now(),
                                metadata: {
                                    version: '1.0',
                                    source: 'migration',
                                    deviceInfo: null,
                                    preferences: {}
                                }
                            };
                            
                            // 保存用户核心数据
                            const coreKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.CORE);
                            await window.storageService.put(coreKey, userCore);
                            
                            // 转换游戏数据格式
                            const gameData = {
                                ...UserDataStructure.getUserGameDataStructure(),
                                progress: {
                                    currentLevel: playerData.gameData?.currentLevel || 1,
                                    unlockedLevels: playerData.gameData?.unlockedLevels || [1],
                                    completedLevels: [],
                                    customLevels: [],
                                    totalPlayTime: 0
                                },
                                stats: playerData.stats || UserDataStructure.getUserGameDataStructure().stats,
                                settings: playerData.settings || UserDataStructure.getUserGameDataStructure().settings,
                                achievements: playerData.gameData?.achievements ? 
                                    { unlocked: playerData.gameData.achievements, progress: {}, notifications: [] } :
                                    UserDataStructure.getUserGameDataStructure().achievements
                            };
                            
                            // 保存游戏数据
                            const gameDataKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.GAME_DATA);
                            await window.storageService.put(gameDataKey, gameData);
                            
                            // 更新用户管理器缓存
                            this.userManager.users.set(identifier, userCore);
                            
                            console.log(`✅ 迁移玩家数据: ${identifier} (${displayName})`);
                        }
                    }
                }
            }
            
            // 保存用户列表
            await this.userManager.saveUserList();
            
        } catch (error) {
            console.error('❌ 迁移玩家数据失败:', error);
        }
    }

    /**
     * 迁移用户凭证数据
     */
    async migrateCredentialData() {
        try {
            const credentialData = await window.storageService.get('user.credential');
            
            if (credentialData && credentialData.identifier) {
                // 使用凭证标识符作为用户标识符
                const userIdentifier = this.sanitizeIdentifier(credentialData.identifier);
                
                // 检查用户是否已存在
                if (!await this.userManager.userExists(userIdentifier)) {
                    // 创建用户（迁移过程中允许使用保留标识符）
                    await this.userManager.createUser(userIdentifier, credentialData.displayName, {
                        type: credentialData.type === 'anonymous' ? 'anonymous' : 'registered',
                        source: 'credential_migration',
                        isSystemInternal: true  // 迁移过程中允许使用保留标识符
                    });
                }
                
                // 保存凭证数据到新格式
                const credentialKey = StorageKeySchema.generateUserKey(userIdentifier, StorageKeySchema.DATA_TYPES.CREDENTIAL);
                await window.storageService.put(credentialKey, credentialData);
                
                console.log(`✅ 迁移用户凭证: ${userIdentifier}`);
            }
            
        } catch (error) {
            console.error('❌ 迁移用户凭证失败:', error);
        }
    }

    /**
     * 清理标识符，确保符合新系统要求
     * @param {string} identifier - 原始标识符
     * @returns {string} 清理后的标识符
     */
    sanitizeIdentifier(identifier) {
        console.log(`🧹 清理标识符: "${identifier}"`);

        // 移除不允许的字符，只保留字母、数字、下划线、连字符
        let sanitized = identifier.replace(/[^a-zA-Z0-9_-]/g, '_');

        // 确保长度在合理范围内
        if (sanitized.length > 32) {
            sanitized = sanitized.substring(0, 32);
        }

        // 确保不以数字开头
        if (/^\d/.test(sanitized)) {
            sanitized = 'user_' + sanitized;
        }

        console.log(`✅ 标识符清理完成: "${identifier}" → "${sanitized}"`);
        return sanitized;
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听用户切换事件，同步到旧系统
        this.userManager.addEventListener('userSwitched', (eventData) => {
            this.syncToLegacySystems(eventData.currentUser);
        });
        
        // 监听用户创建事件
        this.userManager.addEventListener('userCreated', (eventData) => {
            console.log(`🎉 新用户创建: ${eventData.user.displayName}`);
        });
    }

    /**
     * 同步到旧系统
     * @param {Object} currentUser - 当前用户
     */
    async syncToLegacySystems(currentUser) {
        try {
            // 更新用户存储服务的当前用户
            this.userStorageService.setCurrentUser(currentUser.identifier);
            
            // 如果存在PlayerManager，尝试同步
            if (this.playerManager && typeof this.playerManager.switchToPlayer === 'function') {
                // 注意：这里需要小心处理，避免循环调用
                console.log('🔄 同步到PlayerManager...');
            }
            
            // 如果存在UserCredentialSystem，尝试同步
            if (this.userCredentialSystem) {
                console.log('🔄 同步到UserCredentialSystem...');
            }
            
        } catch (error) {
            console.error('❌ 同步到旧系统失败:', error);
        }
    }

    /**
     * 获取用户管理器实例
     * @returns {UserManager} 用户管理器
     */
    getUserManager() {
        return this.userManager;
    }

    /**
     * 获取用户存储服务实例
     * @returns {UserStorageService} 用户存储服务
     */
    getUserStorageService() {
        return this.userStorageService;
    }
}

// 创建全局用户系统适配器实例
if (typeof window !== 'undefined') {
    window.UserSystemAdapter = UserSystemAdapter;
    window.userSystemAdapter = new UserSystemAdapter();
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserSystemAdapter;
}
