<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理器实例引用修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .instance-comparison {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .instance-item {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .instance-label {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
        }
        .instance-value {
            font-size: 14px;
            margin-top: 5px;
            font-family: monospace;
        }
        .test-input {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-input input {
            width: 200px;
            padding: 5px;
            margin: 0 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 用户管理器实例引用修复验证</h1>
        
        <div class="test-section">
            <h3>📋 修复内容概述</h3>
            <div class="result info">
✅ 第三轮修复的问题：
1. 修复了 UserManagementUI 中的 refreshUserManagerReference 方法，确保始终获取最新实例
2. 在 updateUserDisplay 方法中添加了实例一致性检查，强制更新不一致的引用
3. 在全局监听器中添加了实例验证，自动修复 UserManagementUI 的实例引用
4. 优化了 UserManagementUI 的初始化过程，使用统一的引用获取方法
5. 增强了详细的实例验证和日志，便于问题排查

🎯 预期效果：
- UserManagementUI 中的 this.userManager 与全局 userManager 始终是同一个实例
- UserManagementUI.updateUserDisplay 中的 getCurrentUser() 返回正确的用户对象
- 用户认证创建完成后，界面显示正确的用户信息而不是"游客"
- 实例一致性检查确保引用始终正确
            </div>
        </div>

        <div class="test-section">
            <h3>📊 实例引用对比</h3>
            <div class="instance-comparison">
                <div class="instance-item">
                    <div class="instance-label">全局 UserManager</div>
                    <div class="instance-value" id="global-manager">未检查</div>
                </div>
                <div class="instance-item">
                    <div class="instance-label">UI UserManager</div>
                    <div class="instance-value" id="ui-manager">未检查</div>
                </div>
                <div class="instance-item">
                    <div class="instance-label">实例一致性</div>
                    <div class="instance-value" id="instance-consistency">未检查</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 实例引用测试</h3>
            <div class="test-input">
                <label>测试用户名：</label>
                <input type="text" id="test-username" value="testuser" placeholder="输入用户名">
                <button onclick="testInstanceReferences()">测试实例引用</button>
            </div>
            <button onclick="loadGame()">加载游戏</button>
            <button onclick="checkInstances()" id="check-instances-btn" disabled>检查实例</button>
            <button onclick="testFullAuthFlow()" id="test-full-flow-btn" disabled>测试完整认证流程</button>
            <div id="instance-test-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏界面</h3>
            <iframe id="game-frame" src="about:blank"></iframe>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="console-logs" class="result log"></div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let logContainer = null;

        function init() {
            gameFrame = document.getElementById('game-frame');
            logContainer = document.getElementById('console-logs');
            
            // 拦截控制台输出
            interceptConsole();
        }

        function interceptConsole() {
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.log = function(...args) {
                originalLog.apply(console, args);
                logMessage(args.join(' '), 'info');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                logMessage(args.join(' '), 'error');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                logMessage(args.join(' '), 'warning');
            };
        }

        function logMessage(message, type = 'info') {
            if (!logContainer) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `result ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }

        function updateInstanceDisplay(globalManager, uiManager, isConsistent) {
            document.getElementById('global-manager').textContent = globalManager || '未找到';
            document.getElementById('ui-manager').textContent = uiManager || '未找到';
            
            const consistencyElement = document.getElementById('instance-consistency');
            if (isConsistent === true) {
                consistencyElement.textContent = '✅ 一致';
                consistencyElement.style.color = '#28a745';
            } else if (isConsistent === false) {
                consistencyElement.textContent = '❌ 不一致';
                consistencyElement.style.color = '#dc3545';
            } else {
                consistencyElement.textContent = '⏳ 检查中';
                consistencyElement.style.color = '#ffc107';
            }
        }

        function loadGame() {
            addResult('instance-test-results', '🎮 正在加载游戏...', 'info');
            gameFrame.src = 'index.html';
            
            gameFrame.onload = function() {
                addResult('instance-test-results', '✅ 游戏加载完成', 'success');
                document.getElementById('check-instances-btn').disabled = false;
                document.getElementById('test-full-flow-btn').disabled = false;
                
                // 等待一段时间让游戏完全初始化
                setTimeout(() => {
                    addResult('instance-test-results', '⏳ 游戏系统初始化中...', 'info');
                    checkInstances();
                }, 2000);
            };
        }

        function checkInstances() {
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('instance-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 获取全局用户管理器
                let globalManager = null;
                let globalManagerInfo = '未找到';
                
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.getUserManager) {
                    globalManager = gameWindow.userSystemAdapter.getUserManager();
                    if (globalManager) {
                        globalManagerInfo = `${globalManager.constructor.name} (${globalManager.initialized ? '已初始化' : '未初始化'})`;
                    }
                }
                
                // 获取 UI 用户管理器
                let uiManager = null;
                let uiManagerInfo = '未找到';
                
                if (gameWindow.userManagementUI && gameWindow.userManagementUI.userManager) {
                    uiManager = gameWindow.userManagementUI.userManager;
                    if (uiManager) {
                        uiManagerInfo = `${uiManager.constructor.name} (${uiManager.initialized ? '已初始化' : '未初始化'})`;
                    }
                }
                
                // 检查一致性
                const isConsistent = globalManager && uiManager && globalManager === uiManager;
                
                updateInstanceDisplay(globalManagerInfo, uiManagerInfo, isConsistent);
                
                addResult('instance-test-results', `📋 全局管理器: ${globalManagerInfo}`, 'info');
                addResult('instance-test-results', `📋 UI管理器: ${uiManagerInfo}`, 'info');
                addResult('instance-test-results', `📋 实例一致性: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`, isConsistent ? 'success' : 'error');
                
                // 检查当前用户状态
                if (globalManager && typeof globalManager.getCurrentUser === 'function') {
                    const globalUser = globalManager.getCurrentUser();
                    addResult('instance-test-results', `📋 全局当前用户: ${globalUser ? globalUser.displayName : 'null'}`, 'info');
                }
                
                if (uiManager && typeof uiManager.getCurrentUser === 'function') {
                    const uiUser = uiManager.getCurrentUser();
                    addResult('instance-test-results', `📋 UI当前用户: ${uiUser ? uiUser.displayName : 'null'}`, 'info');
                }
                
            } catch (error) {
                addResult('instance-test-results', `❌ 检查过程出错: ${error.message}`, 'error');
                updateInstanceDisplay('检查失败', '检查失败', null);
            }
        }

        function testInstanceReferences() {
            clearResults('instance-test-results');
            addResult('instance-test-results', '🧪 开始测试实例引用...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('instance-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 强制刷新 UserManagementUI 的引用
                if (gameWindow.userManagementUI && gameWindow.userManagementUI.refreshUserManagerReference) {
                    addResult('instance-test-results', '🔄 强制刷新 UserManagementUI 引用...', 'info');
                    gameWindow.userManagementUI.refreshUserManagerReference();
                }
                
                // 等待一段时间后检查
                setTimeout(() => {
                    checkInstances();
                }, 500);
                
            } catch (error) {
                addResult('instance-test-results', `❌ 测试过程出错: ${error.message}`, 'error');
            }
        }

        function testFullAuthFlow() {
            clearResults('instance-test-results');
            addResult('instance-test-results', '🔐 开始测试完整认证流程...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('instance-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                const username = document.getElementById('test-username').value.trim();
                
                if (!username) {
                    addResult('instance-test-results', '❌ 请输入用户名', 'error');
                    return;
                }
                
                // 检查认证前的实例状态
                addResult('instance-test-results', '📋 认证前实例状态检查...', 'info');
                checkInstances();
                
                // 执行认证
                const authResult = {
                    type: 'username_suffix',
                    username: username,
                    userConsent: true,
                    sessionOnly: false
                };
                
                addResult('instance-test-results', `🔐 开始认证用户: ${username}`, 'info');
                
                if (gameWindow.gameApplication && gameWindow.gameApplication.handleUserAuthentication) {
                    gameWindow.gameApplication.handleUserAuthentication(authResult)
                        .then(() => {
                            addResult('instance-test-results', '✅ 认证流程完成', 'success');
                            
                            // 延迟检查认证后的状态
                            setTimeout(() => {
                                addResult('instance-test-results', '📋 认证后实例状态检查...', 'info');
                                checkInstances();
                                
                                // 测试 UI 更新
                                if (gameWindow.userManagementUI && gameWindow.userManagementUI.updateUserDisplay) {
                                    addResult('instance-test-results', '🔄 测试 UI 更新...', 'info');
                                    gameWindow.userManagementUI.updateUserDisplay();
                                }
                            }, 1000);
                        })
                        .catch(error => {
                            addResult('instance-test-results', `❌ 认证失败: ${error.message}`, 'error');
                        });
                } else {
                    addResult('instance-test-results', '❌ 游戏认证方法不可用', 'error');
                }
                
            } catch (error) {
                addResult('instance-test-results', `❌ 测试过程出错: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
