<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化问题修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .test-results {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        .game-frame {
            width: 100%;
            height: 600px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-pending { background-color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JavaScript应用程序初始化问题修复验证</h1>
        
        <div class="test-section">
            <h2>📋 修复内容概述</h2>
            <div class="test-results">
                <div class="info">✅ 修复1: 难度排行榜管理器循环依赖问题</div>
                <div class="info">   - 重构了初始化逻辑，提前设置initialized状态</div>
                <div class="info">   - 为getLeaderboard方法添加skipInitCheck参数</div>
                <div class="info">   - 避免在初始化过程中产生误导性警告</div>
                <div class="info"></div>
                <div class="info">✅ 修复2: 屏幕管理器模块缺失问题</div>
                <div class="info">   - 增强了initModule方法的模块检查逻辑</div>
                <div class="info">   - 添加了详细的调试信息输出</div>
                <div class="info">   - 优化了全局作用域模块获取机制</div>
                <div class="info"></div>
                <div class="info">✅ 修复3: 模块初始化顺序优化</div>
                <div class="info">   - 按依赖关系重新组织初始化顺序</div>
                <div class="info">   - 分层初始化：基础服务 -> 配置 -> 核心 -> 扩展 -> UI</div>
                <div class="info">   - 避免循环依赖和缺失依赖问题</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎮 游戏加载测试</h2>
            <button class="btn" onclick="loadGame()">加载游戏并测试初始化</button>
            <button class="btn" onclick="checkInitializationStatus()" id="check-btn" disabled>检查初始化状态</button>
            <button class="btn" onclick="testModuleDependencies()" id="deps-btn" disabled>测试模块依赖</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
            
            <div id="test-results" class="test-results">
                点击"加载游戏并测试初始化"开始验证修复效果...
            </div>
        </div>

        <div class="test-section">
            <h2>📊 初始化状态监控</h2>
            <div id="status-monitor" class="test-results">
                等待游戏加载...
            </div>
        </div>

        <div class="test-section" style="display: none;" id="game-container">
            <h2>🎯 游戏实例</h2>
            <iframe id="game-frame" class="game-frame" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let monitorInterval = null;

        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 
                               type === 'warning' ? 'status-warning' : 'status-pending';
            
            results.innerHTML += `<div class="${type}">
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${message}
            </div>`;
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const monitor = document.getElementById('status-monitor');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 
                               type === 'warning' ? 'status-warning' : 'status-pending';
            
            monitor.innerHTML += `<div class="${type}">
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${message}
            </div>`;
            monitor.scrollTop = monitor.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('status-monitor').innerHTML = '';
        }

        function loadGame() {
            addResult('🔄 开始加载游戏...', 'info');
            
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            container.style.display = 'block';
            frame.src = './index.html';
            
            frame.onload = function() {
                gameFrame = frame;
                addResult('✅ 游戏页面加载完成', 'success');
                
                // 启用其他按钮
                document.getElementById('check-btn').disabled = false;
                document.getElementById('deps-btn').disabled = false;
                
                // 开始监控初始化过程
                startInitializationMonitoring();
                
                // 等待一段时间后进行初始检查
                setTimeout(() => {
                    checkInitializationStatus();
                }, 3000);
            };

            frame.onerror = function(error) {
                addResult(`❌ 游戏加载失败: ${error}`, 'error');
            };
        }

        function startInitializationMonitoring() {
            updateStatus('🔍 开始监控初始化过程...', 'info');
            
            let checkCount = 0;
            monitorInterval = setInterval(() => {
                checkCount++;
                
                if (!gameFrame || !gameFrame.contentWindow) {
                    return;
                }

                try {
                    const gameWindow = gameFrame.contentWindow;
                    
                    // 检查关键模块
                    const modules = [
                        'storageService',
                        'difficultyConfigManager', 
                        'difficultyLeaderboardManager',
                        'screenManager',
                        'gameApp'
                    ];
                    
                    let initializedCount = 0;
                    modules.forEach(moduleName => {
                        const module = gameWindow[moduleName];
                        if (module && module.initialized) {
                            initializedCount++;
                        }
                    });
                    
                    updateStatus(`📊 模块初始化进度: ${initializedCount}/${modules.length} (检查次数: ${checkCount})`, 'info');
                    
                    // 如果所有模块都初始化完成，停止监控
                    if (initializedCount === modules.length) {
                        clearInterval(monitorInterval);
                        updateStatus('🎉 所有模块初始化完成！', 'success');
                    }
                    
                    // 最多监控30秒
                    if (checkCount >= 60) {
                        clearInterval(monitorInterval);
                        updateStatus('⏰ 监控超时，停止检查', 'warning');
                    }
                    
                } catch (error) {
                    updateStatus(`⚠️ 监控过程中出错: ${error.message}`, 'warning');
                }
            }, 500);
        }

        function checkInitializationStatus() {
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('❌ 游戏未加载或无法访问', 'error');
                return;
            }

            addResult('🔍 检查初始化状态...', 'info');
            
            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查难度排行榜管理器
                const difficultyLeaderboardManager = gameWindow.difficultyLeaderboardManager;
                if (difficultyLeaderboardManager) {
                    if (difficultyLeaderboardManager.initialized) {
                        addResult('✅ 难度排行榜管理器已正确初始化', 'success');
                    } else {
                        addResult('⚠️ 难度排行榜管理器未初始化', 'warning');
                    }
                } else {
                    addResult('❌ 难度排行榜管理器未找到', 'error');
                }
                
                // 检查屏幕管理器
                const screenManager = gameWindow.screenManager;
                if (screenManager) {
                    if (screenManager.initialized) {
                        addResult('✅ 屏幕管理器已正确初始化', 'success');
                    } else {
                        addResult('⚠️ 屏幕管理器未初始化', 'warning');
                    }
                } else {
                    addResult('❌ 屏幕管理器未找到', 'error');
                }
                
                // 检查游戏应用
                const gameApp = gameWindow.gameApp;
                if (gameApp) {
                    if (gameApp.initialized) {
                        addResult('✅ 游戏应用已正确初始化', 'success');
                    } else {
                        addResult('⚠️ 游戏应用未初始化', 'warning');
                    }
                } else {
                    addResult('❌ 游戏应用未找到', 'error');
                }
                
            } catch (error) {
                addResult(`❌ 检查过程中出错: ${error.message}`, 'error');
            }
        }

        function testModuleDependencies() {
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('❌ 游戏未加载或无法访问', 'error');
                return;
            }

            addResult('🔗 测试模块依赖关系...', 'info');
            
            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 测试难度排行榜管理器的依赖
                const difficultyLeaderboardManager = gameWindow.difficultyLeaderboardManager;
                if (difficultyLeaderboardManager && difficultyLeaderboardManager.initialized) {
                    // 测试getLeaderboard方法是否正常工作
                    const testResult = difficultyLeaderboardManager.getLeaderboard('normal', 'daily_high_score', 5);
                    if (testResult !== null) {
                        addResult('✅ 难度排行榜管理器getLeaderboard方法正常工作', 'success');
                    } else {
                        addResult('⚠️ 难度排行榜管理器getLeaderboard返回null（可能是正常的）', 'warning');
                    }
                }
                
                // 测试屏幕管理器的依赖
                const screenManager = gameWindow.screenManager;
                if (screenManager && screenManager.initialized) {
                    if (typeof screenManager.showScreen === 'function') {
                        addResult('✅ 屏幕管理器showScreen方法可用', 'success');
                    } else {
                        addResult('❌ 屏幕管理器showScreen方法不可用', 'error');
                    }
                }
                
                addResult('🎯 依赖关系测试完成', 'success');
                
            } catch (error) {
                addResult(`❌ 依赖测试过程中出错: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
