<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScreenManager 修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .test-results {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        .game-frame {
            width: 100%;
            height: 600px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-pending { background-color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ScreenManager 模块修复验证</h1>
        
        <div class="test-section">
            <h2>📋 修复内容总结</h2>
            <div class="test-results">
                <div class="success">✅ 修复1: 语法错误</div>
                <div class="info">   - 移除了第945行多余的闭合大括号</div>
                <div class="info">   - 修复了"Unexpected identifier 'saveSettings'"语法错误</div>
                <div class="info"></div>
                <div class="success">✅ 修复2: 脚本加载时机问题</div>
                <div class="info">   - 将全局变量创建延迟到DOM准备好之后</div>
                <div class="info">   - 使用DOMContentLoaded事件确保正确的初始化时机</div>
                <div class="info">   - 添加了DOM状态检查逻辑</div>
                <div class="info"></div>
                <div class="success">✅ 修复3: 模块初始化逻辑优化</div>
                <div class="info">   - 为screenManager创建专门的初始化方法</div>
                <div class="info">   - 添加等待机制，确保实例创建完成后再初始化</div>
                <div class="info">   - 增强错误处理和调试信息</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎮 完整游戏测试</h2>
            <button class="btn" onclick="loadFullGame()">加载完整游戏并测试</button>
            <button class="btn" onclick="checkModuleStatus()" id="check-modules-btn" disabled>检查模块状态</button>
            <button class="btn" onclick="testScreenManager()" id="test-screen-btn" disabled>测试ScreenManager功能</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
            
            <div id="test-results" class="test-results">
                点击"加载完整游戏并测试"开始验证修复效果...
            </div>
        </div>

        <div class="test-section">
            <h2>📊 实时状态监控</h2>
            <div id="status-monitor" class="test-results">
                等待游戏加载...
            </div>
        </div>

        <div class="test-section" style="display: none;" id="game-container">
            <h2>🎯 游戏实例</h2>
            <iframe id="game-frame" class="game-frame" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let monitorInterval = null;

        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 
                               type === 'warning' ? 'status-warning' : 'status-pending';
            
            results.innerHTML += `<div class="${type}">
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${message}
            </div>`;
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const monitor = document.getElementById('status-monitor');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 
                               type === 'warning' ? 'status-warning' : 'status-pending';
            
            monitor.innerHTML += `<div class="${type}">
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${message}
            </div>`;
            monitor.scrollTop = monitor.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('status-monitor').innerHTML = '';
        }

        function loadFullGame() {
            addResult('🔄 开始加载完整游戏...', 'info');
            
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            container.style.display = 'block';
            frame.src = './index.html';
            
            frame.onload = function() {
                gameFrame = frame;
                addResult('✅ 游戏页面加载完成', 'success');
                
                // 启用其他按钮
                document.getElementById('check-modules-btn').disabled = false;
                document.getElementById('test-screen-btn').disabled = false;
                
                // 开始监控初始化过程
                startInitializationMonitoring();
                
                // 等待一段时间后进行检查
                setTimeout(() => {
                    checkModuleStatus();
                }, 5000);
            };

            frame.onerror = function(error) {
                addResult(`❌ 游戏加载失败: ${error}`, 'error');
            };
        }

        function startInitializationMonitoring() {
            updateStatus('🔍 开始监控初始化过程...', 'info');
            
            let checkCount = 0;
            monitorInterval = setInterval(() => {
                checkCount++;
                
                if (!gameFrame || !gameFrame.contentWindow) {
                    return;
                }

                try {
                    const gameWindow = gameFrame.contentWindow;
                    
                    // 重点检查screenManager
                    const screenManager = gameWindow.screenManager;
                    if (screenManager) {
                        updateStatus(`✅ screenManager已创建 (检查次数: ${checkCount})`, 'success');
                        
                        if (screenManager.initialized) {
                            updateStatus('🎉 screenManager已初始化完成！', 'success');
                            clearInterval(monitorInterval);
                            return;
                        }
                    } else {
                        updateStatus(`⏳ 等待screenManager创建... (检查次数: ${checkCount})`, 'info');
                    }
                    
                    // 最多监控30秒
                    if (checkCount >= 60) {
                        clearInterval(monitorInterval);
                        updateStatus('⏰ 监控超时，停止检查', 'warning');
                    }
                    
                } catch (error) {
                    updateStatus(`⚠️ 监控过程中出错: ${error.message}`, 'warning');
                }
            }, 500);
        }

        function checkModuleStatus() {
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('❌ 游戏未加载或无法访问', 'error');
                return;
            }

            addResult('🔍 检查模块状态...', 'info');
            
            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查screenManager
                const screenManager = gameWindow.screenManager;
                if (screenManager) {
                    addResult('✅ screenManager模块已找到', 'success');
                    
                    if (screenManager.initialized) {
                        addResult('✅ screenManager已正确初始化', 'success');
                    } else {
                        addResult('⚠️ screenManager未初始化', 'warning');
                    }
                    
                    // 检查关键方法
                    const methods = ['init', 'showScreen', 'registerScreens'];
                    methods.forEach(method => {
                        if (typeof screenManager[method] === 'function') {
                            addResult(`✅ ${method}方法可用`, 'success');
                        } else {
                            addResult(`❌ ${method}方法不可用`, 'error');
                        }
                    });
                } else {
                    addResult('❌ screenManager模块未找到', 'error');
                }
                
                // 检查其他关键模块
                const modules = ['gameApp', 'gameEngine', 'playerManager'];
                modules.forEach(moduleName => {
                    const module = gameWindow[moduleName];
                    if (module) {
                        const status = module.initialized ? '已初始化' : '未初始化';
                        addResult(`📦 ${moduleName}: ${status}`, module.initialized ? 'success' : 'warning');
                    } else {
                        addResult(`❌ ${moduleName}: 未找到`, 'error');
                    }
                });
                
            } catch (error) {
                addResult(`❌ 检查过程中出错: ${error.message}`, 'error');
            }
        }

        function testScreenManager() {
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('❌ 游戏未加载或无法访问', 'error');
                return;
            }

            addResult('🧪 测试ScreenManager功能...', 'info');
            
            try {
                const gameWindow = gameFrame.contentWindow;
                const screenManager = gameWindow.screenManager;
                
                if (!screenManager) {
                    addResult('❌ screenManager不可用', 'error');
                    return;
                }
                
                // 测试屏幕切换功能
                if (typeof screenManager.showScreen === 'function') {
                    addResult('🔄 测试屏幕切换功能...', 'info');
                    
                    // 尝试切换到主菜单
                    try {
                        screenManager.showScreen('main-menu');
                        addResult('✅ 屏幕切换功能正常', 'success');
                    } catch (error) {
                        addResult(`❌ 屏幕切换失败: ${error.message}`, 'error');
                    }
                } else {
                    addResult('❌ showScreen方法不可用', 'error');
                }
                
                // 检查屏幕注册情况
                if (screenManager.screens && screenManager.screens.size > 0) {
                    addResult(`✅ 已注册${screenManager.screens.size}个屏幕`, 'success');
                } else {
                    addResult('⚠️ 没有注册的屏幕', 'warning');
                }
                
                addResult('🎯 ScreenManager功能测试完成', 'success');
                
            } catch (error) {
                addResult(`❌ 测试过程中出错: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
