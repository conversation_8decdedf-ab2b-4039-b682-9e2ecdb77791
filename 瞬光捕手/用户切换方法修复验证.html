<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户切换方法修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .method-name {
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 用户切换方法修复验证</h1>
        
        <div class="test-section">
            <h3>📋 修复内容概述</h3>
            <div class="result info">
✅ 已修复的问题：
1. 将 switchToUser 方法中的 this.refreshUserList() 调用改为 this.updateUserList()
2. 修复了两处方法调用错误（第677行和第690行）
3. 确认其他代码中没有类似的方法名错误

🎯 预期效果：
- 用户切换操作能够正常完成
- 切换完成后能够正确刷新用户列表显示
- 不再出现 "refreshUserList is not a function" 错误
- 用户管理界面能够正常更新
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 方法对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>位置</th>
                        <th>修复前</th>
                        <th>修复后</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>第677行</td>
                        <td><span class="method-name">this.refreshUserList()</span></td>
                        <td><span class="method-name">this.updateUserList()</span></td>
                        <td>用户切换成功时的列表刷新</td>
                    </tr>
                    <tr>
                        <td>第690行</td>
                        <td><span class="method-name">this.refreshUserList()</span></td>
                        <td><span class="method-name">this.updateUserList()</span></td>
                        <td>用户切换部分失败时的列表刷新</td>
                    </tr>
                    <tr>
                        <td>类定义</td>
                        <td>❌ 方法不存在</td>
                        <td>✅ <span class="method-name">updateUserList()</span> 存在</td>
                        <td>正确的方法名</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 方法存在性验证</h3>
            <button onclick="testMethodExistence()">验证方法存在性</button>
            <div id="method-test-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🔄 用户切换功能测试</h3>
            <button onclick="loadGame()">加载游戏</button>
            <button onclick="testUserSwitching()" id="test-switch-btn" disabled>测试用户切换</button>
            <div id="switch-test-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏界面</h3>
            <iframe id="game-frame" src="about:blank"></iframe>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="console-logs" class="result log"></div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let logContainer = null;

        function init() {
            gameFrame = document.getElementById('game-frame');
            logContainer = document.getElementById('console-logs');
            
            // 拦截控制台输出
            interceptConsole();
        }

        function interceptConsole() {
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.log = function(...args) {
                originalLog.apply(console, args);
                logMessage(args.join(' '), 'info');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                logMessage(args.join(' '), 'error');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                logMessage(args.join(' '), 'warning');
            };
        }

        function logMessage(message, type = 'info') {
            if (!logContainer) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `result ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }

        function testMethodExistence() {
            clearResults('method-test-results');
            addResult('method-test-results', '🧪 开始验证方法存在性...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('method-test-results', '❌ 请先加载游戏', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查 UserManagementUI 类
                if (gameWindow.UserManagementUI) {
                    addResult('method-test-results', '✅ UserManagementUI 类存在', 'success');
                    
                    // 检查实例
                    if (gameWindow.userManagementUI) {
                        addResult('method-test-results', '✅ userManagementUI 实例存在', 'success');
                        
                        const ui = gameWindow.userManagementUI;
                        
                        // 检查方法存在性
                        const methods = [
                            { name: 'updateUserList', expected: true },
                            { name: 'refreshUserList', expected: false },
                            { name: 'switchToUser', expected: true },
                            { name: 'updateUserDisplay', expected: true }
                        ];
                        
                        methods.forEach(method => {
                            const exists = typeof ui[method.name] === 'function';
                            const result = exists === method.expected ? '✅' : '❌';
                            const type = exists === method.expected ? 'success' : 'error';
                            
                            addResult('method-test-results', 
                                `${result} ${method.name}: ${exists ? '存在' : '不存在'} (预期: ${method.expected ? '存在' : '不存在'})`, 
                                type
                            );
                        });
                        
                    } else {
                        addResult('method-test-results', '❌ userManagementUI 实例不存在', 'error');
                    }
                } else {
                    addResult('method-test-results', '❌ UserManagementUI 类不存在', 'error');
                }
                
                addResult('method-test-results', '📋 方法存在性验证完成', 'info');
                
            } catch (error) {
                addResult('method-test-results', `❌ 验证过程出错: ${error.message}`, 'error');
            }
        }

        function loadGame() {
            addResult('switch-test-results', '🎮 正在加载游戏...', 'info');
            gameFrame.src = 'index.html';
            
            gameFrame.onload = function() {
                addResult('switch-test-results', '✅ 游戏加载完成', 'success');
                document.getElementById('test-switch-btn').disabled = false;
                
                // 等待一段时间让游戏完全初始化
                setTimeout(() => {
                    testMethodExistence();
                }, 2000);
            };
        }

        function testUserSwitching() {
            clearResults('switch-test-results');
            addResult('switch-test-results', '🔄 开始测试用户切换功能...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('switch-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查必要的组件
                if (!gameWindow.userManagementUI) {
                    addResult('switch-test-results', '❌ 用户管理界面未加载', 'error');
                    return;
                }
                
                if (!gameWindow.userManager && !gameWindow.userSystemAdapter) {
                    addResult('switch-test-results', '❌ 用户管理器未加载', 'error');
                    return;
                }
                
                addResult('switch-test-results', '✅ 系统组件检查通过', 'success');
                
                // 获取用户管理器
                let userManager = gameWindow.userManager;
                if (!userManager && gameWindow.userSystemAdapter) {
                    userManager = gameWindow.userSystemAdapter.getUserManager();
                }
                
                if (!userManager) {
                    addResult('switch-test-results', '❌ 无法获取用户管理器', 'error');
                    return;
                }
                
                // 检查是否有用户可以切换
                const users = userManager.getAllUsers();
                if (!users || users.length < 2) {
                    addResult('switch-test-results', '⚠️ 需要至少2个用户才能测试切换功能', 'warning');
                    addResult('switch-test-results', '💡 请先创建一些用户，然后再测试切换功能', 'info');
                    return;
                }
                
                addResult('switch-test-results', `✅ 发现 ${users.length} 个用户，可以进行切换测试`, 'success');
                
                // 获取当前用户和目标用户
                const currentUser = userManager.getCurrentUser();
                const targetUser = users.find(user => !currentUser || user.identifier !== currentUser.identifier);
                
                if (!targetUser) {
                    addResult('switch-test-results', '❌ 找不到可切换的目标用户', 'error');
                    return;
                }
                
                addResult('switch-test-results', `🎯 准备切换到用户: ${targetUser.displayName} (${targetUser.identifier})`, 'info');
                
                // 监听切换过程中的错误
                const originalError = console.error;
                let hasError = false;
                let errorMessage = '';
                
                console.error = function(...args) {
                    originalError.apply(console, args);
                    const message = args.join(' ');
                    if (message.includes('refreshUserList is not a function')) {
                        hasError = true;
                        errorMessage = message;
                    }
                };
                
                // 执行用户切换
                gameWindow.userManagementUI.switchToUser(targetUser.identifier)
                    .then(() => {
                        // 恢复原始 console.error
                        console.error = originalError;
                        
                        if (hasError) {
                            addResult('switch-test-results', '❌ 仍然存在 refreshUserList 错误！', 'error');
                            addResult('switch-test-results', `错误信息: ${errorMessage}`, 'error');
                        } else {
                            addResult('switch-test-results', '✅ 用户切换成功，没有方法调用错误', 'success');
                            addResult('switch-test-results', '🎉 用户切换方法修复成功！', 'success');
                        }
                    })
                    .catch(error => {
                        // 恢复原始 console.error
                        console.error = originalError;
                        
                        if (error.message.includes('refreshUserList is not a function')) {
                            addResult('switch-test-results', '❌ 仍然存在 refreshUserList 方法错误！', 'error');
                        } else {
                            addResult('switch-test-results', `⚠️ 切换过程中出现其他错误: ${error.message}`, 'warning');
                        }
                    });
                    
            } catch (error) {
                addResult('switch-test-results', `❌ 测试过程出错: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
