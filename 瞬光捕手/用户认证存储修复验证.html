<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户认证存储修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 用户认证存储修复验证</h1>
        
        <div class="test-section">
            <h3>📋 修复内容概述</h3>
            <div class="result info">
✅ 已修复的问题：
1. 在 StorageService 类中添加了 set 方法作为 put 方法的别名
2. 修复了 UserCredentialSystem.saveCredential 方法中的存储服务调用
3. 统一了所有存储服务调用，确保使用正确的全局变量引用
4. 为所有存储服务类添加了向后兼容的 set 方法

🎯 预期效果：
- 用户认证流程能够正常完成
- 用户凭据能够成功保存到存储服务
- 不再出现 "storageService.set is not a function" 错误
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 存储服务API验证</h3>
            <button onclick="testStorageServiceAPI()">验证存储服务API</button>
            <div id="storage-api-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🔐 用户认证系统测试</h3>
            <button onclick="loadGame()">加载游戏</button>
            <button onclick="testUserAuthentication()" id="test-auth-btn" disabled>测试用户认证</button>
            <div id="auth-test-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏界面</h3>
            <iframe id="game-frame" src="about:blank"></iframe>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="console-logs" class="result log"></div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let logContainer = null;

        function init() {
            gameFrame = document.getElementById('game-frame');
            logContainer = document.getElementById('console-logs');
            
            // 拦截控制台输出
            interceptConsole();
        }

        function interceptConsole() {
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.log = function(...args) {
                originalLog.apply(console, args);
                logMessage(args.join(' '), 'info');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                logMessage(args.join(' '), 'error');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                logMessage(args.join(' '), 'warning');
            };
        }

        function logMessage(message, type = 'info') {
            if (!logContainer) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `result ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }

        function testStorageServiceAPI() {
            clearResults('storage-api-results');
            addResult('storage-api-results', '🧪 开始验证存储服务API...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('storage-api-results', '❌ 请先加载游戏', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查存储服务是否存在
                if (gameWindow.storageService) {
                    addResult('storage-api-results', '✅ storageService 模块已加载', 'success');
                    
                    const storageService = gameWindow.storageService;
                    
                    // 检查关键方法
                    const methods = ['put', 'set', 'get', 'delete'];
                    methods.forEach(method => {
                        if (typeof storageService[method] === 'function') {
                            addResult('storage-api-results', `✅ ${method} 方法存在`, 'success');
                        } else {
                            addResult('storage-api-results', `❌ ${method} 方法不存在`, 'error');
                        }
                    });
                    
                    // 检查初始化状态
                    if (storageService.initialized) {
                        addResult('storage-api-results', '✅ 存储服务已初始化', 'success');
                    } else {
                        addResult('storage-api-results', '⚠️ 存储服务未完成初始化', 'warning');
                    }
                    
                } else {
                    addResult('storage-api-results', '❌ storageService 模块未加载', 'error');
                }
                
                addResult('storage-api-results', '📋 存储API验证完成', 'info');
                
            } catch (error) {
                addResult('storage-api-results', `❌ 验证过程出错: ${error.message}`, 'error');
            }
        }

        function loadGame() {
            addResult('auth-test-results', '🎮 正在加载游戏...', 'info');
            gameFrame.src = 'index.html';
            
            gameFrame.onload = function() {
                addResult('auth-test-results', '✅ 游戏加载完成', 'success');
                document.getElementById('test-auth-btn').disabled = false;
                
                // 等待一段时间让游戏完全初始化
                setTimeout(() => {
                    testStorageServiceAPI();
                }, 2000);
            };
        }

        function testUserAuthentication() {
            clearResults('auth-test-results');
            addResult('auth-test-results', '🔐 开始测试用户认证...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('auth-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查用户认证系统
                if (gameWindow.userCredentialSystem) {
                    addResult('auth-test-results', '✅ 用户认证系统已加载', 'success');
                    
                    // 模拟用户名认证流程
                    const testUsername = 'test123';
                    addResult('auth-test-results', `🧪 测试用户名认证: ${testUsername}`, 'info');
                    
                    gameWindow.userCredentialSystem.createUsernameSuffixCredential(testUsername)
                        .then(credential => {
                            if (credential) {
                                addResult('auth-test-results', '✅ 用户名认证成功', 'success');
                                addResult('auth-test-results', `📋 生成的凭据: ${credential.fullDisplayName}`, 'info');
                                addResult('auth-test-results', '✅ 凭据保存成功 - 修复生效！', 'success');
                            } else {
                                addResult('auth-test-results', '❌ 用户名认证失败', 'error');
                            }
                        })
                        .catch(error => {
                            addResult('auth-test-results', `❌ 认证过程出错: ${error.message}`, 'error');
                            if (error.message.includes('storageService.set is not a function')) {
                                addResult('auth-test-results', '🚨 仍然存在存储服务方法错误！', 'error');
                            }
                        });
                        
                } else {
                    addResult('auth-test-results', '❌ 用户认证系统未加载', 'error');
                }
                
            } catch (error) {
                addResult('auth-test-results', `❌ 测试过程出错: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
