<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户标识符格式修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .format-example {
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 用户标识符格式修复验证</h1>
        
        <div class="test-section">
            <h3>📋 修复内容概述</h3>
            <div class="result info">
✅ 已修复的问题：
1. 将用户标识符生成格式从 "username#suffix" 改为 "username_suffix"
2. 更新了相关文档中的格式示例
3. 在主应用程序中添加了标识符清理逻辑
4. 确保生成的标识符符合 StorageKeySchema 验证规则

🎯 预期效果：
- 用户认证流程能够正常完成
- 生成的标识符能够通过存储键验证
- 不再出现 "用户标识符格式无效" 错误
- 整个认证流程能够完整执行
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 格式对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>修复前</th>
                        <th>修复后</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>标识符格式</td>
                        <td><span class="format-example">123#DO88</span></td>
                        <td><span class="format-example">123_DO88</span></td>
                        <td>使用下划线代替井号</td>
                    </tr>
                    <tr>
                        <td>允许字符</td>
                        <td>字母、数字、下划线、连字符、<strong>井号</strong></td>
                        <td>字母、数字、下划线、连字符</td>
                        <td>移除井号字符</td>
                    </tr>
                    <tr>
                        <td>验证规则</td>
                        <td><span class="format-example">/^[a-zA-Z0-9_-]+$/</span></td>
                        <td><span class="format-example">/^[a-zA-Z0-9_-]+$/</span></td>
                        <td>保持一致</td>
                    </tr>
                    <tr>
                        <td>示例标识符</td>
                        <td><span class="format-example">张三#A1B2</span></td>
                        <td><span class="format-example">张三_A1B2</span></td>
                        <td>格式统一</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 标识符格式验证</h3>
            <button onclick="testIdentifierFormats()">测试标识符格式</button>
            <div id="format-test-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🔐 用户认证流程测试</h3>
            <button onclick="loadGame()">加载游戏</button>
            <button onclick="testUserAuthenticationFlow()" id="test-flow-btn" disabled>测试完整认证流程</button>
            <div id="auth-flow-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏界面</h3>
            <iframe id="game-frame" src="about:blank"></iframe>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="console-logs" class="result log"></div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let logContainer = null;

        function init() {
            gameFrame = document.getElementById('game-frame');
            logContainer = document.getElementById('console-logs');
            
            // 拦截控制台输出
            interceptConsole();
        }

        function interceptConsole() {
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.log = function(...args) {
                originalLog.apply(console, args);
                logMessage(args.join(' '), 'info');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                logMessage(args.join(' '), 'error');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                logMessage(args.join(' '), 'warning');
            };
        }

        function logMessage(message, type = 'info') {
            if (!logContainer) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `result ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }

        function testIdentifierFormats() {
            clearResults('format-test-results');
            addResult('format-test-results', '🧪 开始测试标识符格式...', 'info');
            
            // 测试用例
            const testCases = [
                { input: '123_DO88', expected: true, description: '修复后的格式（下划线）' },
                { input: '123#DO88', expected: false, description: '修复前的格式（井号）' },
                { input: 'user_123', expected: true, description: '标准用户名格式' },
                { input: 'test-user', expected: true, description: '连字符格式' },
                { input: 'user@123', expected: false, description: '包含非法字符' },
                { input: 'user.123', expected: false, description: '包含点号' },
                { input: 'user 123', expected: false, description: '包含空格' },
                { input: 'user123', expected: true, description: '纯字母数字' }
            ];
            
            const validationRegex = /^[a-zA-Z0-9_-]+$/;
            
            testCases.forEach(testCase => {
                const isValid = validationRegex.test(testCase.input);
                const result = isValid === testCase.expected ? '✅' : '❌';
                const type = isValid === testCase.expected ? 'success' : 'error';
                
                addResult('format-test-results', 
                    `${result} "${testCase.input}" - ${testCase.description} (预期: ${testCase.expected ? '有效' : '无效'}, 实际: ${isValid ? '有效' : '无效'})`, 
                    type
                );
            });
            
            addResult('format-test-results', '📋 标识符格式测试完成', 'info');
        }

        function loadGame() {
            addResult('auth-flow-results', '🎮 正在加载游戏...', 'info');
            gameFrame.src = 'index.html';
            
            gameFrame.onload = function() {
                addResult('auth-flow-results', '✅ 游戏加载完成', 'success');
                document.getElementById('test-flow-btn').disabled = false;
                
                // 等待一段时间让游戏完全初始化
                setTimeout(() => {
                    addResult('auth-flow-results', '⏳ 游戏系统初始化中...', 'info');
                }, 2000);
            };
        }

        function testUserAuthenticationFlow() {
            clearResults('auth-flow-results');
            addResult('auth-flow-results', '🔐 开始测试完整认证流程...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('auth-flow-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查必要的系统组件
                if (!gameWindow.userCredentialSystem) {
                    addResult('auth-flow-results', '❌ 用户认证系统未加载', 'error');
                    return;
                }
                
                if (!gameWindow.userSystemAdapter) {
                    addResult('auth-flow-results', '❌ 用户系统适配器未加载', 'error');
                    return;
                }
                
                addResult('auth-flow-results', '✅ 系统组件检查通过', 'success');
                
                // 测试标识符生成和清理
                const testUsername = 'test123';
                addResult('auth-flow-results', `🧪 测试用户名: ${testUsername}`, 'info');
                
                gameWindow.userCredentialSystem.createUsernameSuffixCredential(testUsername)
                    .then(credential => {
                        if (credential) {
                            addResult('auth-flow-results', `✅ 凭证生成成功: ${credential.fullDisplayName}`, 'success');
                            
                            // 测试标识符清理
                            const sanitized = gameWindow.userSystemAdapter.sanitizeIdentifier(credential.identifier);
                            addResult('auth-flow-results', `🔄 标识符清理: "${credential.identifier}" → "${sanitized}"`, 'info');
                            
                            // 验证清理后的标识符格式
                            const validationRegex = /^[a-zA-Z0-9_-]+$/;
                            if (validationRegex.test(sanitized)) {
                                addResult('auth-flow-results', '✅ 清理后的标识符格式验证通过', 'success');
                                addResult('auth-flow-results', '🎉 用户标识符格式修复成功！', 'success');
                            } else {
                                addResult('auth-flow-results', '❌ 清理后的标识符格式仍然无效', 'error');
                            }
                        } else {
                            addResult('auth-flow-results', '❌ 凭证生成失败', 'error');
                        }
                    })
                    .catch(error => {
                        addResult('auth-flow-results', `❌ 认证流程出错: ${error.message}`, 'error');
                        if (error.message.includes('用户标识符格式无效')) {
                            addResult('auth-flow-results', '🚨 仍然存在标识符格式问题！', 'error');
                        }
                    });
                    
            } catch (error) {
                addResult('auth-flow-results', `❌ 测试过程出错: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
