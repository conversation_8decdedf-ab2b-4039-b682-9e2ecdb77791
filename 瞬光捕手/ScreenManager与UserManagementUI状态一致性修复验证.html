<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScreenManager与UserManagementUI状态一致性修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .component-comparison {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .component-item {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .component-label {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
        }
        .component-value {
            font-size: 14px;
            margin-top: 5px;
            font-family: monospace;
        }
        .test-input {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-input input {
            width: 200px;
            padding: 5px;
            margin: 0 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .status-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 ScreenManager与UserManagementUI状态一致性修复验证</h1>
        
        <div class="test-section">
            <h3>📋 修复内容概述</h3>
            <div class="result info">
✅ 修复的问题：
1. 修复了 ScreenManager 中的用户管理器引用方式，使其与 UserManagementUI 保持一致
2. 将 ScreenManager 的用户信息获取改为通过 userSystemAdapter.getUserManager() 获取最新实例
3. 添加了 ScreenManager 与 UserManagementUI 的实例一致性验证
4. 增强了 ScreenManager 的状态验证和详细日志
5. 优化了 ScreenManager 的事件监听器设置，确保使用正确的实例

🎯 预期效果：
- ScreenManager 和 UserManagementUI 使用相同的 UserManager 实例
- 两个组件获取到相同的用户状态信息
- 用户认证创建完成后，所有界面组件显示一致的用户信息
- 不再出现 UserManagementUI 显示正确用户而 ScreenManager 显示"游客"的问题
            </div>
        </div>

        <div class="test-section">
            <h3>📊 组件状态对比</h3>
            <div class="component-comparison">
                <div class="component-item">
                    <div class="component-label">UserManagementUI</div>
                    <div class="component-value" id="ui-status">未检查</div>
                </div>
                <div class="component-item">
                    <div class="component-label">ScreenManager</div>
                    <div class="component-value" id="screen-status">未检查</div>
                </div>
                <div class="component-item">
                    <div class="component-label">状态一致性</div>
                    <div class="component-value" id="consistency-status">未检查</div>
                </div>
            </div>
            
            <div class="status-grid">
                <div class="status-item">
                    <div class="component-label">UI 实例类型</div>
                    <div class="component-value" id="ui-instance-type">未知</div>
                </div>
                <div class="status-item">
                    <div class="component-label">Screen 实例类型</div>
                    <div class="component-value" id="screen-instance-type">未知</div>
                </div>
                <div class="status-item">
                    <div class="component-label">实例一致性</div>
                    <div class="component-value" id="instance-consistency">未知</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 状态一致性测试</h3>
            <div class="test-input">
                <label>测试用户名：</label>
                <input type="text" id="test-username" value="testuser" placeholder="输入用户名">
                <button onclick="testConsistency()">测试状态一致性</button>
            </div>
            <button onclick="loadGame()">加载游戏</button>
            <button onclick="checkComponentStates()" id="check-states-btn" disabled>检查组件状态</button>
            <button onclick="testFullConsistencyFlow()" id="test-consistency-btn" disabled>测试完整一致性流程</button>
            <button onclick="forceUpdateBothComponents()" id="force-update-btn" disabled>强制更新两个组件</button>
            <div id="consistency-test-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏界面</h3>
            <iframe id="game-frame" src="about:blank"></iframe>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="console-logs" class="result log"></div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let logContainer = null;

        function init() {
            gameFrame = document.getElementById('game-frame');
            logContainer = document.getElementById('console-logs');
            
            // 拦截控制台输出
            interceptConsole();
        }

        function interceptConsole() {
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.log = function(...args) {
                originalLog.apply(console, args);
                logMessage(args.join(' '), 'info');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                logMessage(args.join(' '), 'error');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                logMessage(args.join(' '), 'warning');
            };
        }

        function logMessage(message, type = 'info') {
            if (!logContainer) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `result ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }

        function updateComponentStatus(uiStatus, screenStatus, isConsistent, uiInstanceType, screenInstanceType, instanceConsistent) {
            document.getElementById('ui-status').textContent = uiStatus || '未知';
            document.getElementById('screen-status').textContent = screenStatus || '未知';
            document.getElementById('ui-instance-type').textContent = uiInstanceType || '未知';
            document.getElementById('screen-instance-type').textContent = screenInstanceType || '未知';
            
            const consistencyElement = document.getElementById('consistency-status');
            const instanceElement = document.getElementById('instance-consistency');
            
            if (isConsistent === true) {
                consistencyElement.textContent = '✅ 一致';
                consistencyElement.style.color = '#28a745';
            } else if (isConsistent === false) {
                consistencyElement.textContent = '❌ 不一致';
                consistencyElement.style.color = '#dc3545';
            } else {
                consistencyElement.textContent = '⏳ 检查中';
                consistencyElement.style.color = '#ffc107';
            }
            
            if (instanceConsistent === true) {
                instanceElement.textContent = '✅ 相同';
                instanceElement.style.color = '#28a745';
            } else if (instanceConsistent === false) {
                instanceElement.textContent = '❌ 不同';
                instanceElement.style.color = '#dc3545';
            } else {
                instanceElement.textContent = '⏳ 检查中';
                instanceElement.style.color = '#ffc107';
            }
        }

        function loadGame() {
            addResult('consistency-test-results', '🎮 正在加载游戏...', 'info');
            gameFrame.src = 'index.html';
            
            gameFrame.onload = function() {
                addResult('consistency-test-results', '✅ 游戏加载完成', 'success');
                document.getElementById('check-states-btn').disabled = false;
                document.getElementById('test-consistency-btn').disabled = false;
                document.getElementById('force-update-btn').disabled = false;
                
                // 等待一段时间让游戏完全初始化
                setTimeout(() => {
                    addResult('consistency-test-results', '⏳ 游戏系统初始化中...', 'info');
                    checkComponentStates();
                }, 2000);
            };
        }

        function checkComponentStates() {
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('consistency-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查 UserManagementUI 状态
                let uiStatus = '未找到';
                let uiInstanceType = '未知';
                let uiUser = null;
                
                if (gameWindow.userManagementUI && gameWindow.userManagementUI.userManager) {
                    const uiManager = gameWindow.userManagementUI.userManager;
                    uiInstanceType = uiManager.constructor.name;
                    uiUser = uiManager.getCurrentUser();
                    uiStatus = uiUser ? uiUser.displayName : '游客';
                }
                
                // 检查 ScreenManager 状态
                let screenStatus = '未找到';
                let screenInstanceType = '未知';
                let screenUser = null;
                let screenManager = null;
                
                if (gameWindow.screenManager) {
                    // 模拟 ScreenManager 的用户获取逻辑
                    if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.getUserManager) {
                        screenManager = gameWindow.userSystemAdapter.getUserManager();
                        screenInstanceType = screenManager.constructor.name;
                        screenUser = screenManager.getCurrentUser();
                        screenStatus = screenUser ? screenUser.displayName : '游客';
                    }
                }
                
                // 检查一致性
                const isConsistent = uiStatus === screenStatus;
                const instanceConsistent = gameWindow.userManagementUI && 
                                         gameWindow.userManagementUI.userManager === screenManager;
                
                updateComponentStatus(uiStatus, screenStatus, isConsistent, uiInstanceType, screenInstanceType, instanceConsistent);
                
                addResult('consistency-test-results', `📋 UserManagementUI 状态: ${uiStatus} (${uiInstanceType})`, 'info');
                addResult('consistency-test-results', `📋 ScreenManager 状态: ${screenStatus} (${screenInstanceType})`, 'info');
                addResult('consistency-test-results', `📋 状态一致性: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`, isConsistent ? 'success' : 'error');
                addResult('consistency-test-results', `📋 实例一致性: ${instanceConsistent ? '✅ 相同' : '❌ 不同'}`, instanceConsistent ? 'success' : 'error');
                
            } catch (error) {
                addResult('consistency-test-results', `❌ 检查过程出错: ${error.message}`, 'error');
                updateComponentStatus('检查失败', '检查失败', null, '未知', '未知', null);
            }
        }

        function testConsistency() {
            clearResults('consistency-test-results');
            addResult('consistency-test-results', '🧪 开始测试状态一致性...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('consistency-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 强制刷新两个组件的用户管理器引用
                if (gameWindow.userManagementUI && gameWindow.userManagementUI.refreshUserManagerReference) {
                    addResult('consistency-test-results', '🔄 刷新 UserManagementUI 引用...', 'info');
                    gameWindow.userManagementUI.refreshUserManagerReference();
                }
                
                // 等待一段时间后检查
                setTimeout(() => {
                    checkComponentStates();
                }, 500);
                
            } catch (error) {
                addResult('consistency-test-results', `❌ 测试过程出错: ${error.message}`, 'error');
            }
        }

        function testFullConsistencyFlow() {
            clearResults('consistency-test-results');
            addResult('consistency-test-results', '🔐 开始测试完整一致性流程...', 'info');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('consistency-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                const username = document.getElementById('test-username').value.trim();
                
                if (!username) {
                    addResult('consistency-test-results', '❌ 请输入用户名', 'error');
                    return;
                }
                
                // 检查认证前的状态
                addResult('consistency-test-results', '📋 认证前状态检查...', 'info');
                checkComponentStates();
                
                // 执行认证
                const authResult = {
                    type: 'username_suffix',
                    username: username,
                    userConsent: true,
                    sessionOnly: false
                };
                
                addResult('consistency-test-results', `🔐 开始认证用户: ${username}`, 'info');
                
                if (gameWindow.gameApplication && gameWindow.gameApplication.handleUserAuthentication) {
                    gameWindow.gameApplication.handleUserAuthentication(authResult)
                        .then(() => {
                            addResult('consistency-test-results', '✅ 认证流程完成', 'success');
                            
                            // 延迟检查认证后的状态
                            setTimeout(() => {
                                addResult('consistency-test-results', '📋 认证后状态检查...', 'info');
                                checkComponentStates();
                            }, 1000);
                        })
                        .catch(error => {
                            addResult('consistency-test-results', `❌ 认证失败: ${error.message}`, 'error');
                        });
                } else {
                    addResult('consistency-test-results', '❌ 游戏认证方法不可用', 'error');
                }
                
            } catch (error) {
                addResult('consistency-test-results', `❌ 测试过程出错: ${error.message}`, 'error');
            }
        }

        function forceUpdateBothComponents() {
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('consistency-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                addResult('consistency-test-results', '🔄 强制更新两个组件...', 'info');
                
                // 强制更新 UserManagementUI
                if (gameWindow.userManagementUI && gameWindow.userManagementUI.updateUserDisplay) {
                    gameWindow.userManagementUI.updateUserDisplay();
                    addResult('consistency-test-results', '✅ 已强制更新 UserManagementUI', 'success');
                }
                
                // 强制更新 ScreenManager
                if (gameWindow.screenManager && gameWindow.screenManager.updateUserDisplayInAllScreens) {
                    gameWindow.screenManager.updateUserDisplayInAllScreens();
                    addResult('consistency-test-results', '✅ 已强制更新 ScreenManager', 'success');
                }
                
                // 延迟检查更新后的状态
                setTimeout(() => {
                    checkComponentStates();
                }, 500);
                
            } catch (error) {
                addResult('consistency-test-results', `❌ 强制更新过程出错: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
