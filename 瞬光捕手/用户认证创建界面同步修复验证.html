<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户认证创建界面同步修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status-display {
            display: flex;
            gap: 20px;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .status-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .status-label {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
        }
        .status-value {
            font-size: 16px;
            margin-top: 5px;
            font-family: monospace;
        }
        .timeline {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .timeline-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            padding-left: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-input {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-input input {
            width: 200px;
            padding: 5px;
            margin: 0 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 用户认证创建界面同步修复验证</h1>
        
        <div class="test-section">
            <h3>📋 修复内容概述</h3>
            <div class="result info">
✅ 第二轮修复的问题：
1. 添加了防抖机制，防止全局监听器重复触发 forceUpdateAllUserDisplays
2. 优化了 UserManagementUI.updateUserDisplay 方法，添加详细的状态检查和日志
3. 添加了 refreshUserManagerReference 方法，确保用户管理器引用正确
4. 移除了重复的事件监听器，避免用户切换和创建事件被多次处理
5. 增强了状态验证和错误日志，便于问题排查

🎯 预期效果：
- 用户认证和创建完成后，界面显示正确的用户信息
- 不再出现"游客"显示而实际已创建新用户的问题
- 不再出现重复的日志输出和重复的界面更新调用
- 所有界面元素同步更新新用户信息
- 界面显示与后端状态保持一致
            </div>
        </div>

        <div class="test-section">
            <h3>📊 实时状态监控</h3>
            <div class="status-display">
                <div class="status-item">
                    <div class="status-label">后端当前用户</div>
                    <div class="status-value" id="backend-user">未知</div>
                </div>
                <div class="status-item">
                    <div class="status-label">界面显示用户</div>
                    <div class="status-value" id="ui-user">未知</div>
                </div>
                <div class="status-item">
                    <div class="status-label">状态一致性</div>
                    <div class="status-value" id="consistency-status">检查中</div>
                </div>
                <div class="status-item">
                    <div class="status-label">认证状态</div>
                    <div class="status-value" id="auth-status">未认证</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 用户认证创建测试</h3>
            <div class="test-input">
                <label>测试用户名：</label>
                <input type="text" id="test-username" value="testuser" placeholder="输入用户名">
                <button onclick="simulateUserAuth()">模拟用户认证</button>
            </div>
            <button onclick="loadGame()">加载游戏</button>
            <button onclick="startMonitoring()" id="monitor-btn" disabled>开始监控</button>
            <button onclick="testAuthFlow()" id="test-auth-btn" disabled>测试认证流程</button>
            <button onclick="checkConsistency()" id="check-btn" disabled>检查一致性</button>
            <div id="auth-test-results" class="result log"></div>
        </div>

        <div class="test-section">
            <h3>⏱️ 认证时间线</h3>
            <div id="timeline" class="timeline"></div>
        </div>

        <div class="test-section">
            <h3>🎮 游戏界面</h3>
            <iframe id="game-frame" src="about:blank"></iframe>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="console-logs" class="result log"></div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let logContainer = null;
        let monitoringInterval = null;
        let timelineContainer = null;

        function init() {
            gameFrame = document.getElementById('game-frame');
            logContainer = document.getElementById('console-logs');
            timelineContainer = document.getElementById('timeline');
            
            // 拦截控制台输出
            interceptConsole();
        }

        function interceptConsole() {
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.log = function(...args) {
                originalLog.apply(console, args);
                logMessage(args.join(' '), 'info');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                logMessage(args.join(' '), 'error');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                logMessage(args.join(' '), 'warning');
            };
        }

        function logMessage(message, type = 'info') {
            if (!logContainer) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `result ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function addTimelineEntry(message) {
            if (!timelineContainer) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'timeline-entry';
            entry.textContent = `[${timestamp}] ${message}`;
            
            timelineContainer.appendChild(entry);
            timelineContainer.scrollTop = timelineContainer.scrollHeight;
        }

        function clearLogs() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
            if (timelineContainer) {
                timelineContainer.innerHTML = '';
            }
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }

        function updateStatus(backendUser, uiUser, authStatus = '未知') {
            document.getElementById('backend-user').textContent = backendUser || '未知';
            document.getElementById('ui-user').textContent = uiUser || '未知';
            document.getElementById('auth-status').textContent = authStatus;
            
            const consistent = backendUser && uiUser && backendUser === uiUser;
            const statusElement = document.getElementById('consistency-status');
            
            if (consistent) {
                statusElement.textContent = '✅ 一致';
                statusElement.style.color = '#28a745';
            } else if (backendUser && uiUser) {
                statusElement.textContent = '❌ 不一致';
                statusElement.style.color = '#dc3545';
            } else {
                statusElement.textContent = '⏳ 检查中';
                statusElement.style.color = '#ffc107';
            }
        }

        function loadGame() {
            addResult('auth-test-results', '🎮 正在加载游戏...', 'info');
            gameFrame.src = 'index.html';
            
            gameFrame.onload = function() {
                addResult('auth-test-results', '✅ 游戏加载完成', 'success');
                document.getElementById('monitor-btn').disabled = false;
                document.getElementById('test-auth-btn').disabled = false;
                document.getElementById('check-btn').disabled = false;
                
                // 等待一段时间让游戏完全初始化
                setTimeout(() => {
                    addResult('auth-test-results', '⏳ 游戏系统初始化中...', 'info');
                    checkConsistency();
                }, 2000);
            };
        }

        function startMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                document.getElementById('monitor-btn').textContent = '开始监控';
                addResult('auth-test-results', '⏹️ 停止状态监控', 'info');
                return;
            }

            document.getElementById('monitor-btn').textContent = '停止监控';
            addResult('auth-test-results', '▶️ 开始状态监控', 'info');
            
            monitoringInterval = setInterval(() => {
                checkConsistency(false);
            }, 1000);
        }

        function checkConsistency(showLog = true) {
            if (!gameFrame || !gameFrame.contentWindow) {
                updateStatus('游戏未加载', '游戏未加载');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 获取后端用户信息
                let backendUser = '游客';
                let authStatus = '未认证';
                
                if (gameWindow.userManager && gameWindow.userManager.getCurrentUser) {
                    const currentUser = gameWindow.userManager.getCurrentUser();
                    if (currentUser) {
                        backendUser = currentUser.displayName;
                        authStatus = currentUser.type === 'guest' ? '游客模式' : '已认证';
                    }
                }
                
                // 获取界面显示的用户信息
                let uiUser = '未知';
                const userNameDisplay = gameWindow.document.getElementById('user-name-display');
                const currentUserDisplay = gameWindow.document.getElementById('current-user-display');
                const currentUserName = gameWindow.document.getElementById('current-user-name');
                
                if (userNameDisplay) {
                    uiUser = userNameDisplay.textContent;
                } else if (currentUserDisplay) {
                    uiUser = currentUserDisplay.textContent;
                } else if (currentUserName) {
                    uiUser = currentUserName.textContent;
                }
                
                updateStatus(backendUser, uiUser, authStatus);
                
                if (showLog) {
                    const consistent = backendUser === uiUser;
                    if (consistent) {
                        addResult('auth-test-results', `✅ 状态一致: ${backendUser}`, 'success');
                    } else {
                        addResult('auth-test-results', `❌ 状态不一致: 后端=${backendUser}, 界面=${uiUser}`, 'error');
                    }
                }
                
            } catch (error) {
                if (showLog) {
                    addResult('auth-test-results', `❌ 检查过程出错: ${error.message}`, 'error');
                }
                updateStatus('检查失败', '检查失败');
            }
        }

        function simulateUserAuth() {
            const username = document.getElementById('test-username').value.trim();
            if (!username) {
                addResult('auth-test-results', '❌ 请输入用户名', 'error');
                return;
            }
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('auth-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 模拟认证结果
                const authResult = {
                    type: 'username_suffix',
                    username: username,
                    userConsent: true,
                    sessionOnly: false
                };
                
                addResult('auth-test-results', `🔐 模拟用户认证: ${username}`, 'info');
                addTimelineEntry(`开始模拟认证: ${username}`);
                
                // 调用游戏的认证处理方法
                if (gameWindow.gameApplication && gameWindow.gameApplication.handleUserAuthentication) {
                    gameWindow.gameApplication.handleUserAuthentication(authResult)
                        .then(() => {
                            addResult('auth-test-results', '✅ 模拟认证完成', 'success');
                            addTimelineEntry('认证流程完成');
                            
                            // 延迟检查状态
                            setTimeout(() => {
                                checkConsistency();
                            }, 500);
                        })
                        .catch(error => {
                            addResult('auth-test-results', `❌ 模拟认证失败: ${error.message}`, 'error');
                            addTimelineEntry(`认证失败: ${error.message}`);
                        });
                } else {
                    addResult('auth-test-results', '❌ 游戏认证方法不可用', 'error');
                }
                
            } catch (error) {
                addResult('auth-test-results', `❌ 模拟认证出错: ${error.message}`, 'error');
                addTimelineEntry(`认证出错: ${error.message}`);
            }
        }

        function testAuthFlow() {
            clearResults('auth-test-results');
            addResult('auth-test-results', '🔐 开始测试用户认证创建流程...', 'info');
            addTimelineEntry('开始认证创建流程测试');
            
            if (!gameFrame || !gameFrame.contentWindow) {
                addResult('auth-test-results', '❌ 游戏未加载', 'error');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                
                // 检查必要的组件
                if (!gameWindow.userCredentialSystem) {
                    addResult('auth-test-results', '❌ 用户认证系统未加载', 'error');
                    return;
                }
                
                if (!gameWindow.userManager && !gameWindow.userSystemAdapter) {
                    addResult('auth-test-results', '❌ 用户管理器未加载', 'error');
                    return;
                }
                
                addResult('auth-test-results', '✅ 系统组件检查通过', 'success');
                
                // 生成测试用户名
                const testUsername = 'test' + Math.floor(Math.random() * 1000);
                addResult('auth-test-results', `🧪 测试用户名: ${testUsername}`, 'info');
                addTimelineEntry(`生成测试用户: ${testUsername}`);
                
                // 记录认证前的状态
                const beforeUser = gameWindow.userManager ? 
                    (gameWindow.userManager.getCurrentUser() ? gameWindow.userManager.getCurrentUser().displayName : '游客') : 
                    '未知';
                addTimelineEntry(`认证前状态: ${beforeUser}`);
                
                // 执行认证流程并监控状态变化
                let authCompleted = false;
                let checkCount = 0;
                const maxChecks = 100; // 最多检查10秒
                
                const monitorAuth = () => {
                    checkCount++;
                    
                    const currentUser = gameWindow.userManager ? gameWindow.userManager.getCurrentUser() : null;
                    const backendName = currentUser ? currentUser.displayName : '游客';
                    
                    // 获取界面显示
                    const userNameDisplay = gameWindow.document.getElementById('user-name-display');
                    const uiName = userNameDisplay ? userNameDisplay.textContent : '未知';
                    
                    addTimelineEntry(`检查 ${checkCount}: 后端=${backendName}, 界面=${uiName}`);
                    
                    if (backendName === testUsername && uiName === testUsername) {
                        authCompleted = true;
                        addResult('auth-test-results', '✅ 用户认证创建成功，界面同步正常！', 'success');
                        addTimelineEntry('✅ 认证创建完成，状态同步');
                        updateStatus(backendName, uiName, '已认证');
                        return;
                    }
                    
                    if (checkCount >= maxChecks) {
                        addResult('auth-test-results', '❌ 认证超时或界面同步失败', 'error');
                        addTimelineEntry('❌ 认证超时');
                        updateStatus(backendName, uiName, '超时');
                        return;
                    }
                    
                    setTimeout(monitorAuth, 100);
                };
                
                // 开始监控
                setTimeout(monitorAuth, 100);
                
                // 执行认证
                const authResult = {
                    type: 'username_suffix',
                    username: testUsername,
                    userConsent: true,
                    sessionOnly: false
                };
                
                if (gameWindow.gameApplication && gameWindow.gameApplication.handleUserAuthentication) {
                    gameWindow.gameApplication.handleUserAuthentication(authResult)
                        .then(() => {
                            addTimelineEntry('认证处理完成');
                        })
                        .catch(error => {
                            addResult('auth-test-results', `❌ 认证失败: ${error.message}`, 'error');
                            addTimelineEntry(`认证失败: ${error.message}`);
                        });
                } else {
                    addResult('auth-test-results', '❌ 游戏认证方法不可用', 'error');
                }
                
            } catch (error) {
                addResult('auth-test-results', `❌ 测试过程出错: ${error.message}`, 'error');
                addTimelineEntry(`测试出错: ${error.message}`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
