/**
 * 时空织梦者 - 统一KV存储服务
 * 支持多种存储后端的适配器模式
 * 
 * 功能特性:
 * - 自动选择最佳存储方案 (IndexedDB > localStorage > Memory)
 * - 统一的异步API接口
 * - 错误处理和降级机制
 * - 数据序列化和反序列化
 * - 前缀支持和批量操作
 */

class StorageService {
    constructor() {
        this.adapter = null;
        this.isInitialized = false;
        this.initPromise = null;
        
        // 存储适配器优先级
        this.adapterPriority = [
            'indexeddb',
            'localstorage', 
            'memory'
        ];
        
        console.log('🗄️ 存储服务初始化中...');
    }

    /**
     * 初始化存储服务
     * 自动选择最佳可用的存储适配器
     */
    async init() {
        if (this.isInitialized) {
            return this.adapter;
        }

        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = this._initializeAdapter();
        return this.initPromise;
    }

    async _initializeAdapter() {
        for (const adapterType of this.adapterPriority) {
            try {
                const adapter = this._createAdapter(adapterType);
                await adapter.init();
                
                this.adapter = adapter;
                this.isInitialized = true;
                
                console.log(`✅ 存储服务已启用: ${adapterType.toUpperCase()}`);
                return adapter;
            } catch (error) {
                console.warn(`⚠️ ${adapterType.toUpperCase()} 适配器初始化失败:`, error.message);
                continue;
            }
        }

        throw new Error('❌ 所有存储适配器初始化失败');
    }

    _createAdapter(type) {
        switch (type) {
            case 'indexeddb':
                return new IndexedDBAdapter();
            case 'localstorage':
                return new LocalStorageAdapter();
            case 'memory':
                return new MemoryAdapter();
            default:
                throw new Error(`未知的存储适配器类型: ${type}`);
        }
    }

    /**
     * 保存键值对数据
     * @param {string} key - 存储键
     * @param {any} value - 存储值
     * @returns {Promise<void>}
     */
    async put(key, value) {
        await this.init();
        return this.adapter.put(key, value);
    }

    /**
     * 保存键值对数据 (set 方法别名，用于向后兼容)
     * @param {string} key - 存储键
     * @param {any} value - 存储值
     * @returns {Promise<void>}
     */
    async set(key, value) {
        console.log(`🔄 调用 set 方法 (兼容性别名)，键: "${key}"`);
        return await this.put(key, value);
    }

    /**
     * 获取指定键的数据
     * @param {string} key - 存储键
     * @param {any} defaultValue - 默认值
     * @returns {Promise<any>}
     */
    async get(key, defaultValue = null) {
        await this.init();
        return this.adapter.get(key, defaultValue);
    }

    /**
     * 删除指定键的数据
     * @param {string} key - 存储键
     * @returns {Promise<void>}
     */
    async delete(key) {
        await this.init();
        return this.adapter.delete(key);
    }

    /**
     * 列出指定前缀的所有键
     * @param {string} prefix - 键前缀
     * @returns {Promise<string[]>}
     */
    async list(prefix = '') {
        await this.init();
        return this.adapter.list(prefix);
    }

    /**
     * 清空所有数据
     * @returns {Promise<void>}
     */
    async clear() {
        await this.init();
        return this.adapter.clear();
    }

    /**
     * 获取存储统计信息
     * @returns {Promise<object>}
     */
    async getStats() {
        await this.init();
        return this.adapter.getStats();
    }
}

/**
 * IndexedDB 存储适配器
 * 提供最佳的存储性能和容量
 */
class IndexedDBAdapter {
    constructor() {
        this.dbName = 'TemporalDreamWeaverDB';
        this.dbVersion = 1;
        this.storeName = 'gameData';
        this.db = null;
    }

    async init() {
        if (!window.indexedDB) {
            throw new Error('IndexedDB 不受支持');
        }

        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('IndexedDB 打开失败'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains(this.storeName)) {
                    db.createObjectStore(this.storeName, { keyPath: 'key' });
                }
            };
        });
    }

    async put(key, value) {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        
        const data = {
            key: key,
            value: JSON.stringify(value),
            timestamp: Date.now()
        };

        return new Promise((resolve, reject) => {
            const request = store.put(data);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(new Error('IndexedDB 写入失败'));
        });
    }

    async get(key, defaultValue = null) {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);

        return new Promise((resolve, reject) => {
            const request = store.get(key);
            
            request.onsuccess = (event) => {
                const result = event.target.result;
                if (result) {
                    try {
                        resolve(JSON.parse(result.value));
                    } catch (error) {
                        resolve(result.value);
                    }
                } else {
                    resolve(defaultValue);
                }
            };
            
            request.onerror = () => reject(new Error('IndexedDB 读取失败'));
        });
    }

    async delete(key) {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);

        return new Promise((resolve, reject) => {
            const request = store.delete(key);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(new Error('IndexedDB 删除失败'));
        });
    }

    async list(prefix = '') {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);

        return new Promise((resolve, reject) => {
            const keys = [];
            const request = store.openCursor();

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    const key = cursor.value.key;
                    if (key.startsWith(prefix)) {
                        keys.push(key);
                    }
                    cursor.continue();
                } else {
                    resolve(keys);
                }
            };

            request.onerror = () => reject(new Error('IndexedDB 列表获取失败'));
        });
    }

    async clear() {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);

        return new Promise((resolve, reject) => {
            const request = store.clear();
            request.onsuccess = () => resolve();
            request.onerror = () => reject(new Error('IndexedDB 清空失败'));
        });
    }

    async getStats() {
        const keys = await this.list();
        return {
            type: 'IndexedDB',
            keyCount: keys.length,
            isAvailable: true
        };
    }
}

/**
 * localStorage 存储适配器
 * 提供简单可靠的存储方案
 */
class LocalStorageAdapter {
    constructor() {
        this.prefix = 'tdw_'; // Temporal Dream Weaver prefix
    }

    async init() {
        if (!window.localStorage) {
            throw new Error('localStorage 不受支持');
        }

        // 测试写入权限
        try {
            const testKey = this.prefix + 'test';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
        } catch (error) {
            throw new Error('localStorage 无写入权限');
        }
    }

    async put(key, value) {
        try {
            const data = {
                value: value,
                timestamp: Date.now()
            };
            localStorage.setItem(this.prefix + key, JSON.stringify(data));
        } catch (error) {
            throw new Error('localStorage 存储空间不足');
        }
    }

    async get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            if (item) {
                const data = JSON.parse(item);
                return data.value;
            }
            return defaultValue;
        } catch (error) {
            return defaultValue;
        }
    }

    async delete(key) {
        localStorage.removeItem(this.prefix + key);
    }

    async list(prefix = '') {
        const keys = [];
        const fullPrefix = this.prefix + prefix;
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(fullPrefix)) {
                keys.push(key.substring(this.prefix.length));
            }
        }
        
        return keys;
    }

    async clear() {
        const keys = await this.list();
        keys.forEach(key => {
            localStorage.removeItem(this.prefix + key);
        });
    }

    async getStats() {
        const keys = await this.list();
        return {
            type: 'localStorage',
            keyCount: keys.length,
            isAvailable: true
        };
    }
}

/**
 * 内存存储适配器
 * 临时存储方案，页面刷新后数据丢失
 */
class MemoryAdapter {
    constructor() {
        this.data = new Map();
    }

    async init() {
        // 内存存储总是可用
        console.log('⚠️ 使用内存存储，数据将在页面刷新后丢失');
    }

    async put(key, value) {
        this.data.set(key, {
            value: value,
            timestamp: Date.now()
        });
    }

    async get(key, defaultValue = null) {
        const item = this.data.get(key);
        return item ? item.value : defaultValue;
    }

    async delete(key) {
        this.data.delete(key);
    }

    async list(prefix = '') {
        const keys = [];
        for (const key of this.data.keys()) {
            if (key.startsWith(prefix)) {
                keys.push(key);
            }
        }
        return keys;
    }

    async clear() {
        this.data.clear();
    }

    async getStats() {
        return {
            type: 'Memory',
            keyCount: this.data.size,
            isAvailable: true
        };
    }
}

// 创建全局存储服务实例
const storageService = new StorageService();

// 导出存储服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StorageService, storageService };
} else {
    window.StorageService = StorageService;
    window.storageService = storageService;
}
