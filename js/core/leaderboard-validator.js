/**
 * Split-Second Spark - 排行榜数据验证器
 * 提供全面的数据验证、完整性检查和错误处理功能
 * 
 * 功能特性:
 * - 输入参数验证
 * - 数据完整性检查
 * - 业务规则验证
 * - 防作弊检测
 * - 异常情况处理
 * - 详细的错误报告
 */

class LeaderboardValidator {
    constructor() {
        // 验证规则配置
        this.validationRules = {
            // 玩家ID规则
            playerId: {
                required: true,
                type: 'string',
                minLength: 1,
                maxLength: 50,
                pattern: /^[a-zA-Z0-9_-]+$/,
                blacklist: ['admin', 'system', 'bot', 'test']
            },
            
            // 玩家名称规则
            playerName: {
                required: true,
                type: 'string',
                minLength: 1,
                maxLength: 30,
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$/,
                blacklist: ['管理员', '系统', '机器人', '测试']
            },
            
            // 分数规则
            score: {
                required: true,
                type: 'number',
                min: 0,
                max: 999999999,
                precision: 0 // 整数
            },
            
            // 游戏类型规则
            gameType: {
                required: true,
                type: 'string',
                enum: ['spark-catcher', 'quantum-resonator', 'temporal-weaver']
            },
            
            // 难度等级规则
            difficulty: {
                required: true,
                type: 'string',
                enum: ['beginner', 'easy', 'normal', 'hard', 'expert', 'master']
            },
            
            // 排行榜类别规则
            category: {
                required: true,
                type: 'string',
                enum: [
                    'high_score', 'daily_high_score', 'weekly_high_score', 'monthly_high_score',
                    'perfect_hits', 'combo_record', 'accuracy_rate', 'fastest_completion',
                    'longest_survival', 'level_completion', 'custom_level_score'
                ]
            }
        };
        
        // 业务规则配置
        this.businessRules = {
            // 分数合理性检查
            scoreValidation: {
                maxScorePerSecond: 1000,        // 每秒最大分数
                maxPerfectHitRatio: 0.95,       // 最大完美击中比例
                minGameDuration: 3000,          // 最短游戏时长（毫秒）
                maxGameDuration: 3600000        // 最长游戏时长（1小时）
            },
            
            // 提交频率限制
            submissionLimits: {
                maxSubmissionsPerMinute: 10,    // 每分钟最大提交次数
                maxSubmissionsPerHour: 100,     // 每小时最大提交次数
                maxSubmissionsPerDay: 500       // 每天最大提交次数
            },
            
            // 异常检测阈值
            anomalyDetection: {
                maxConsecutivePerfects: 50,     // 最大连续完美击中
                suspiciousComboLength: 100,     // 可疑连击长度
                impossibleAccuracy: 0.99,      // 不可能的准确率
                scoreJumpThreshold: 10000       // 分数跳跃阈值
            }
        };
        
        // 错误类型定义
        this.errorTypes = {
            VALIDATION_ERROR: 'validation_error',
            BUSINESS_RULE_ERROR: 'business_rule_error',
            INTEGRITY_ERROR: 'integrity_error',
            SECURITY_ERROR: 'security_error',
            RATE_LIMIT_ERROR: 'rate_limit_error'
        };
        
        // 提交历史跟踪（用于频率限制）
        this.submissionHistory = new Map();
        
        // 验证统计
        this.stats = {
            totalValidations: 0,
            successfulValidations: 0,
            failedValidations: 0,
            errorsByType: {},
            lastValidation: null
        };
        
        console.log('🛡️ 排行榜数据验证器已创建');
    }
    
    /**
     * 验证排行榜条目数据
     * @param {Object} entryData - 条目数据
     * @param {Object} options - 验证选项
     * @returns {Object} 验证结果
     */
    validateEntry(entryData, options = {}) {
        const startTime = Date.now();
        const result = {
            valid: true,
            errors: [],
            warnings: [],
            metadata: {
                validationTime: 0,
                rulesChecked: 0,
                severity: 'none'
            }
        };
        
        try {
            this.stats.totalValidations++;
            
            // 1. 基础数据验证
            this.validateBasicData(entryData, result);
            
            // 2. 业务规则验证
            if (options.checkBusinessRules !== false) {
                this.validateBusinessRules(entryData, result);
            }
            
            // 3. 数据完整性检查
            if (options.checkIntegrity !== false) {
                this.validateDataIntegrity(entryData, result);
            }
            
            // 4. 安全性检查
            if (options.checkSecurity !== false) {
                this.validateSecurity(entryData, result);
            }
            
            // 5. 频率限制检查
            if (options.checkRateLimit !== false) {
                this.validateRateLimit(entryData, result);
            }
            
            // 计算验证时间
            result.metadata.validationTime = Date.now() - startTime;
            
            // 确定严重程度
            result.metadata.severity = this.determineSeverity(result);
            
            // 更新统计信息
            if (result.valid) {
                this.stats.successfulValidations++;
            } else {
                this.stats.failedValidations++;
                this.updateErrorStats(result.errors);
            }
            
            this.stats.lastValidation = {
                timestamp: Date.now(),
                valid: result.valid,
                errorCount: result.errors.length,
                warningCount: result.warnings.length
            };
            
            console.log(`🛡️ 数据验证完成: ${result.valid ? '✅ 通过' : '❌ 失败'} (${result.metadata.validationTime}ms)`);
            
            return result;
            
        } catch (error) {
            console.error('❌ 验证过程发生异常:', error);
            
            result.valid = false;
            result.errors.push({
                type: this.errorTypes.VALIDATION_ERROR,
                field: 'system',
                message: '验证过程发生内部错误',
                details: error.message
            });
            
            return result;
        }
    }
    
    /**
     * 验证基础数据
     * @param {Object} data - 数据对象
     * @param {Object} result - 验证结果
     */
    validateBasicData(data, result) {
        for (const [field, rules] of Object.entries(this.validationRules)) {
            result.metadata.rulesChecked++;
            
            const value = data[field];
            
            // 检查必需字段
            if (rules.required && (value === undefined || value === null || value === '')) {
                this.addError(result, this.errorTypes.VALIDATION_ERROR, field, 
                    `字段 ${field} 是必需的`, { expectedType: rules.type });
                continue;
            }
            
            // 如果字段不存在且不是必需的，跳过后续检查
            if (value === undefined || value === null) {
                continue;
            }
            
            // 检查数据类型
            if (rules.type && typeof value !== rules.type) {
                this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                    `字段 ${field} 类型错误，期望 ${rules.type}，实际 ${typeof value}`,
                    { expectedType: rules.type, actualType: typeof value });
                continue;
            }
            
            // 字符串特定验证
            if (rules.type === 'string') {
                this.validateStringField(field, value, rules, result);
            }
            
            // 数字特定验证
            if (rules.type === 'number') {
                this.validateNumberField(field, value, rules, result);
            }
            
            // 枚举值验证
            if (rules.enum && !rules.enum.includes(value)) {
                this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                    `字段 ${field} 值无效，允许的值: ${rules.enum.join(', ')}`,
                    { allowedValues: rules.enum, actualValue: value });
            }
            
            // 黑名单检查
            if (rules.blacklist && rules.blacklist.some(banned => 
                value.toString().toLowerCase().includes(banned.toLowerCase()))) {
                this.addError(result, this.errorTypes.SECURITY_ERROR, field,
                    `字段 ${field} 包含禁用内容`, { value: value });
            }
        }
    }
    
    /**
     * 验证字符串字段
     * @param {string} field - 字段名
     * @param {string} value - 字段值
     * @param {Object} rules - 验证规则
     * @param {Object} result - 验证结果
     */
    validateStringField(field, value, rules, result) {
        // 长度检查
        if (rules.minLength && value.length < rules.minLength) {
            this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                `字段 ${field} 长度不足，最小长度 ${rules.minLength}`,
                { minLength: rules.minLength, actualLength: value.length });
        }
        
        if (rules.maxLength && value.length > rules.maxLength) {
            this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                `字段 ${field} 长度超限，最大长度 ${rules.maxLength}`,
                { maxLength: rules.maxLength, actualLength: value.length });
        }
        
        // 正则表达式检查
        if (rules.pattern && !rules.pattern.test(value)) {
            this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                `字段 ${field} 格式不正确`, { pattern: rules.pattern.toString(), value: value });
        }
    }
    
    /**
     * 验证数字字段
     * @param {string} field - 字段名
     * @param {number} value - 字段值
     * @param {Object} rules - 验证规则
     * @param {Object} result - 验证结果
     */
    validateNumberField(field, value, rules, result) {
        // 范围检查
        if (rules.min !== undefined && value < rules.min) {
            this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                `字段 ${field} 值过小，最小值 ${rules.min}`,
                { min: rules.min, actualValue: value });
        }
        
        if (rules.max !== undefined && value > rules.max) {
            this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                `字段 ${field} 值过大，最大值 ${rules.max}`,
                { max: rules.max, actualValue: value });
        }
        
        // 精度检查
        if (rules.precision !== undefined) {
            const decimalPlaces = (value.toString().split('.')[1] || '').length;
            if (decimalPlaces > rules.precision) {
                this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                    `字段 ${field} 精度过高，最大小数位数 ${rules.precision}`,
                    { maxPrecision: rules.precision, actualPrecision: decimalPlaces });
            }
        }
        
        // 特殊数值检查
        if (!Number.isFinite(value)) {
            this.addError(result, this.errorTypes.VALIDATION_ERROR, field,
                `字段 ${field} 不是有效的数值`, { value: value });
        }
    }
    
    /**
     * 验证业务规则
     * @param {Object} data - 数据对象
     * @param {Object} result - 验证结果
     */
    validateBusinessRules(data, result) {
        const rules = this.businessRules;
        
        // 分数合理性检查
        if (data.gameData && data.gameData.duration) {
            const scorePerSecond = data.score / (data.gameData.duration / 1000);
            if (scorePerSecond > rules.scoreValidation.maxScorePerSecond) {
                this.addError(result, this.errorTypes.BUSINESS_RULE_ERROR, 'score',
                    '分数增长速度异常，可能存在作弊行为',
                    { scorePerSecond: scorePerSecond, maxAllowed: rules.scoreValidation.maxScorePerSecond });
            }
        }
        
        // 完美击中比例检查
        if (data.gameData && data.gameData.perfectHits && data.gameData.totalHits) {
            const perfectRatio = data.gameData.perfectHits / data.gameData.totalHits;
            if (perfectRatio > rules.scoreValidation.maxPerfectHitRatio) {
                this.addWarning(result, 'gameData.perfectHits',
                    '完美击中比例过高，建议进一步验证',
                    { perfectRatio: perfectRatio, maxAllowed: rules.scoreValidation.maxPerfectHitRatio });
            }
        }
        
        // 游戏时长检查
        if (data.gameData && data.gameData.duration) {
            if (data.gameData.duration < rules.scoreValidation.minGameDuration) {
                this.addError(result, this.errorTypes.BUSINESS_RULE_ERROR, 'gameData.duration',
                    '游戏时长过短，可能存在异常',
                    { duration: data.gameData.duration, minRequired: rules.scoreValidation.minGameDuration });
            }
            
            if (data.gameData.duration > rules.scoreValidation.maxGameDuration) {
                this.addError(result, this.errorTypes.BUSINESS_RULE_ERROR, 'gameData.duration',
                    '游戏时长过长，超出合理范围',
                    { duration: data.gameData.duration, maxAllowed: rules.scoreValidation.maxGameDuration });
            }
        }
        
        // 连击长度检查
        if (data.gameData && data.gameData.combo) {
            if (data.gameData.combo > rules.anomalyDetection.suspiciousComboLength) {
                this.addWarning(result, 'gameData.combo',
                    '连击长度异常，建议进一步验证',
                    { combo: data.gameData.combo, threshold: rules.anomalyDetection.suspiciousComboLength });
            }
        }
        
        // 准确率检查
        if (data.gameData && data.gameData.accuracy) {
            if (data.gameData.accuracy > rules.anomalyDetection.impossibleAccuracy) {
                this.addError(result, this.errorTypes.BUSINESS_RULE_ERROR, 'gameData.accuracy',
                    '准确率过高，超出正常范围',
                    { accuracy: data.gameData.accuracy, maxReasonable: rules.anomalyDetection.impossibleAccuracy });
            }
        }
    }
    
    /**
     * 验证数据完整性
     * @param {Object} data - 数据对象
     * @param {Object} result - 验证结果
     */
    validateDataIntegrity(data, result) {
        // 检查必需的游戏数据字段
        if (data.gameData) {
            const requiredGameDataFields = ['duration', 'totalHits', 'accuracy'];
            for (const field of requiredGameDataFields) {
                if (data.gameData[field] === undefined || data.gameData[field] === null) {
                    this.addWarning(result, `gameData.${field}`,
                        `游戏数据字段 ${field} 缺失，可能影响数据完整性`);
                }
            }
            
            // 检查数据一致性
            if (data.gameData.totalHits && data.gameData.missedHits) {
                const hitSum = (data.gameData.perfectHits || 0) + (data.gameData.missedHits || 0);
                if (Math.abs(hitSum - data.gameData.totalHits) > 1) {
                    this.addError(result, this.errorTypes.INTEGRITY_ERROR, 'gameData',
                        '击中数据不一致，总击中数与分项数据不匹配',
                        { totalHits: data.gameData.totalHits, calculatedTotal: hitSum });
                }
            }
        }
        
        // 检查时间戳合理性
        if (data.timestamp) {
            const now = Date.now();
            const timeDiff = Math.abs(now - data.timestamp);
            
            // 时间戳不能太久远（超过1天）
            if (timeDiff > 24 * 60 * 60 * 1000) {
                this.addWarning(result, 'timestamp',
                    '时间戳异常，与当前时间差距过大',
                    { timestamp: data.timestamp, currentTime: now, diffHours: timeDiff / (60 * 60 * 1000) });
            }
            
            // 时间戳不能是未来时间（允许5分钟误差）
            if (data.timestamp > now + 5 * 60 * 1000) {
                this.addError(result, this.errorTypes.INTEGRITY_ERROR, 'timestamp',
                    '时间戳不能是未来时间',
                    { timestamp: data.timestamp, currentTime: now });
            }
        }
    }
    
    /**
     * 验证安全性
     * @param {Object} data - 数据对象
     * @param {Object} result - 验证结果
     */
    validateSecurity(data, result) {
        // 检查SQL注入尝试
        const sqlPatterns = [
            /('|(\\')|(;)|(\\;)|(--)|(\s*(union|select|insert|delete|update|drop|create|alter|exec|execute)\s+)/i
        ];
        
        const stringFields = ['playerId', 'playerName'];
        for (const field of stringFields) {
            if (data[field] && typeof data[field] === 'string') {
                for (const pattern of sqlPatterns) {
                    if (pattern.test(data[field])) {
                        this.addError(result, this.errorTypes.SECURITY_ERROR, field,
                            '检测到潜在的安全威胁',
                            { field: field, value: data[field] });
                        break;
                    }
                }
            }
        }
        
        // 检查XSS尝试
        const xssPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/i,
            /on\w+\s*=/i
        ];
        
        for (const field of stringFields) {
            if (data[field] && typeof data[field] === 'string') {
                for (const pattern of xssPatterns) {
                    if (pattern.test(data[field])) {
                        this.addError(result, this.errorTypes.SECURITY_ERROR, field,
                            '检测到潜在的XSS攻击尝试',
                            { field: field, value: data[field] });
                        break;
                    }
                }
            }
        }
    }
    
    /**
     * 验证提交频率限制
     * @param {Object} data - 数据对象
     * @param {Object} result - 验证结果
     */
    validateRateLimit(data, result) {
        const playerId = data.playerId;
        const now = Date.now();
        
        if (!this.submissionHistory.has(playerId)) {
            this.submissionHistory.set(playerId, []);
        }
        
        const playerHistory = this.submissionHistory.get(playerId);
        
        // 清理过期记录（保留24小时内的记录）
        const dayAgo = now - 24 * 60 * 60 * 1000;
        while (playerHistory.length > 0 && playerHistory[0] < dayAgo) {
            playerHistory.shift();
        }
        
        // 检查各种时间窗口的提交频率
        const limits = this.businessRules.submissionLimits;
        
        // 每分钟限制
        const minuteAgo = now - 60 * 1000;
        const submissionsInMinute = playerHistory.filter(time => time > minuteAgo).length;
        if (submissionsInMinute >= limits.maxSubmissionsPerMinute) {
            this.addError(result, this.errorTypes.RATE_LIMIT_ERROR, 'submission_rate',
                '提交频率过高，请稍后再试',
                { period: 'minute', count: submissionsInMinute, limit: limits.maxSubmissionsPerMinute });
        }
        
        // 每小时限制
        const hourAgo = now - 60 * 60 * 1000;
        const submissionsInHour = playerHistory.filter(time => time > hourAgo).length;
        if (submissionsInHour >= limits.maxSubmissionsPerHour) {
            this.addError(result, this.errorTypes.RATE_LIMIT_ERROR, 'submission_rate',
                '每小时提交次数超限',
                { period: 'hour', count: submissionsInHour, limit: limits.maxSubmissionsPerHour });
        }
        
        // 每天限制
        const submissionsInDay = playerHistory.length;
        if (submissionsInDay >= limits.maxSubmissionsPerDay) {
            this.addError(result, this.errorTypes.RATE_LIMIT_ERROR, 'submission_rate',
                '每日提交次数超限',
                { period: 'day', count: submissionsInDay, limit: limits.maxSubmissionsPerDay });
        }
        
        // 如果验证通过，记录本次提交
        if (result.valid) {
            playerHistory.push(now);
        }
    }
    
    /**
     * 添加错误
     * @param {Object} result - 验证结果
     * @param {string} type - 错误类型
     * @param {string} field - 字段名
     * @param {string} message - 错误消息
     * @param {Object} details - 详细信息
     */
    addError(result, type, field, message, details = {}) {
        result.valid = false;
        result.errors.push({
            type: type,
            field: field,
            message: message,
            details: details,
            timestamp: Date.now()
        });
    }
    
    /**
     * 添加警告
     * @param {Object} result - 验证结果
     * @param {string} field - 字段名
     * @param {string} message - 警告消息
     * @param {Object} details - 详细信息
     */
    addWarning(result, field, message, details = {}) {
        result.warnings.push({
            field: field,
            message: message,
            details: details,
            timestamp: Date.now()
        });
    }
    
    /**
     * 确定严重程度
     * @param {Object} result - 验证结果
     * @returns {string} 严重程度
     */
    determineSeverity(result) {
        if (result.errors.length === 0) {
            return result.warnings.length > 0 ? 'warning' : 'none';
        }
        
        const hasSecurityError = result.errors.some(error => 
            error.type === this.errorTypes.SECURITY_ERROR);
        const hasBusinessRuleError = result.errors.some(error => 
            error.type === this.errorTypes.BUSINESS_RULE_ERROR);
        
        if (hasSecurityError) return 'critical';
        if (hasBusinessRuleError) return 'high';
        return 'medium';
    }
    
    /**
     * 更新错误统计
     * @param {Array} errors - 错误列表
     */
    updateErrorStats(errors) {
        for (const error of errors) {
            if (!this.stats.errorsByType[error.type]) {
                this.stats.errorsByType[error.type] = 0;
            }
            this.stats.errorsByType[error.type]++;
        }
    }
    
    /**
     * 获取验证统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalValidations > 0 ? 
                (this.stats.successfulValidations / this.stats.totalValidations * 100).toFixed(2) + '%' : '0%',
            trackedPlayers: this.submissionHistory.size
        };
    }
    
    /**
     * 清理过期的提交历史
     */
    cleanupSubmissionHistory() {
        const now = Date.now();
        const dayAgo = now - 24 * 60 * 60 * 1000;
        
        for (const [playerId, history] of this.submissionHistory.entries()) {
            // 清理过期记录
            while (history.length > 0 && history[0] < dayAgo) {
                history.shift();
            }
            
            // 如果没有记录了，删除该玩家的历史
            if (history.length === 0) {
                this.submissionHistory.delete(playerId);
            }
        }
        
        console.log(`🧹 清理提交历史完成，当前跟踪 ${this.submissionHistory.size} 个玩家`);
    }
    
    /**
     * 重置验证统计
     */
    resetStats() {
        this.stats = {
            totalValidations: 0,
            successfulValidations: 0,
            failedValidations: 0,
            errorsByType: {},
            lastValidation: null
        };
        
        console.log('📊 验证统计已重置');
    }
}

// 导出排行榜验证器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LeaderboardValidator;
} else if (typeof window !== 'undefined') {
    window.LeaderboardValidator = LeaderboardValidator;
}
