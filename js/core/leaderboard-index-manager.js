/**
 * Split-Second Spark - 排行榜索引管理器
 * 为排行榜查询提供高效的索引策略和缓存机制
 * 
 * 功能特性:
 * - 多维度索引支持（玩家ID、难度、游戏类型、时间范围）
 * - 智能缓存策略
 * - 查询性能优化
 * - 内存使用优化
 * - 索引自动维护
 */

class LeaderboardIndexManager {
    constructor() {
        this.initialized = false;
        
        // 索引存储结构
        this.indexes = {
            // 主索引：按游戏类型 -> 难度 -> 类别 -> 条目列表
            primary: new Map(),
            
            // 玩家索引：按玩家ID -> 条目列表
            byPlayer: new Map(),
            
            // 时间索引：按时间范围 -> 条目列表
            byTime: new Map(),
            
            // 分数索引：按分数范围 -> 条目列表
            byScore: new Map(),
            
            // 复合索引：按多个维度组合
            composite: new Map()
        };
        
        // 缓存配置
        this.cacheConfig = {
            maxSize: 1000,              // 最大缓存条目数
            ttl: 5 * 60 * 1000,        // 缓存生存时间（5分钟）
            cleanupInterval: 60 * 1000  // 清理间隔（1分钟）
        };
        
        // 查询缓存
        this.queryCache = new Map();
        this.cacheStats = {
            hits: 0,
            misses: 0,
            evictions: 0
        };
        
        // 索引统计
        this.indexStats = {
            totalEntries: 0,
            indexSize: 0,
            lastUpdate: 0,
            rebuildCount: 0
        };
        
        // 自动清理定时器
        this.cleanupTimer = null;
        
        console.log('📊 排行榜索引管理器已创建');
    }
    
    /**
     * 初始化索引管理器
     */
    async init() {
        try {
            // 启动缓存清理定时器
            this.startCacheCleanup();
            
            // 加载现有索引数据
            await this.loadIndexes();
            
            this.initialized = true;
            console.log('✅ 排行榜索引管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 排行榜索引管理器初始化失败:', error);
            this.initialized = true; // 即使失败也标记为已初始化
        }
    }
    
    /**
     * 添加条目到索引
     * @param {Object} entry - 排行榜条目
     */
    addToIndex(entry) {
        try {
            // 添加到主索引
            this.addToPrimaryIndex(entry);
            
            // 添加到玩家索引
            this.addToPlayerIndex(entry);
            
            // 添加到时间索引
            this.addToTimeIndex(entry);
            
            // 添加到分数索引
            this.addToScoreIndex(entry);
            
            // 添加到复合索引
            this.addToCompositeIndex(entry);
            
            // 更新统计信息
            this.indexStats.totalEntries++;
            this.indexStats.lastUpdate = Date.now();
            
            // 清除相关缓存
            this.invalidateRelatedCache(entry);
            
            console.log(`📊 条目已添加到索引: ${entry.entryId}`);
            
        } catch (error) {
            console.error('❌ 添加索引失败:', error);
        }
    }
    
    /**
     * 从索引中移除条目
     * @param {Object} entry - 排行榜条目
     */
    removeFromIndex(entry) {
        try {
            // 从各个索引中移除
            this.removeFromPrimaryIndex(entry);
            this.removeFromPlayerIndex(entry);
            this.removeFromTimeIndex(entry);
            this.removeFromScoreIndex(entry);
            this.removeFromCompositeIndex(entry);
            
            // 更新统计信息
            this.indexStats.totalEntries = Math.max(0, this.indexStats.totalEntries - 1);
            this.indexStats.lastUpdate = Date.now();
            
            // 清除相关缓存
            this.invalidateRelatedCache(entry);
            
            console.log(`📊 条目已从索引移除: ${entry.entryId}`);
            
        } catch (error) {
            console.error('❌ 移除索引失败:', error);
        }
    }
    
    /**
     * 查询排行榜数据
     * @param {Object} query - 查询条件
     * @returns {Array} 查询结果
     */
    query(query) {
        const cacheKey = this.generateCacheKey(query);
        
        // 检查缓存
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            this.cacheStats.hits++;
            return cached;
        }
        
        this.cacheStats.misses++;
        
        // 执行查询
        let results = [];
        
        try {
            if (query.playerId) {
                // 按玩家查询
                results = this.queryByPlayer(query);
            } else if (query.gameType && query.difficulty && query.category) {
                // 按主索引查询
                results = this.queryByPrimaryIndex(query);
            } else if (query.timeRange) {
                // 按时间范围查询
                results = this.queryByTimeRange(query);
            } else if (query.scoreRange) {
                // 按分数范围查询
                results = this.queryByScoreRange(query);
            } else {
                // 复合查询
                results = this.queryComposite(query);
            }
            
            // 应用排序和限制
            results = this.applySortingAndLimits(results, query);
            
            // 缓存结果
            this.setCache(cacheKey, results);
            
            return results;
            
        } catch (error) {
            console.error('❌ 查询执行失败:', error);
            return [];
        }
    }
    
    /**
     * 添加到主索引
     * @param {Object} entry - 排行榜条目
     */
    addToPrimaryIndex(entry) {
        const { gameType, difficulty, category } = entry;
        
        if (!this.indexes.primary.has(gameType)) {
            this.indexes.primary.set(gameType, new Map());
        }
        
        const gameIndex = this.indexes.primary.get(gameType);
        if (!gameIndex.has(difficulty)) {
            gameIndex.set(difficulty, new Map());
        }
        
        const difficultyIndex = gameIndex.get(difficulty);
        if (!difficultyIndex.has(category)) {
            difficultyIndex.set(category, []);
        }
        
        const categoryEntries = difficultyIndex.get(category);
        categoryEntries.push(entry);
        
        // 保持排序（按分数降序）
        categoryEntries.sort((a, b) => b.score - a.score);
    }
    
    /**
     * 添加到玩家索引
     * @param {Object} entry - 排行榜条目
     */
    addToPlayerIndex(entry) {
        const { playerId } = entry;
        
        if (!this.indexes.byPlayer.has(playerId)) {
            this.indexes.byPlayer.set(playerId, []);
        }
        
        const playerEntries = this.indexes.byPlayer.get(playerId);
        playerEntries.push(entry);
        
        // 保持时间排序（最新的在前）
        playerEntries.sort((a, b) => b.timestamp - a.timestamp);
    }
    
    /**
     * 添加到时间索引
     * @param {Object} entry - 排行榜条目
     */
    addToTimeIndex(entry) {
        const timeKey = this.getTimeKey(entry.timestamp);
        
        if (!this.indexes.byTime.has(timeKey)) {
            this.indexes.byTime.set(timeKey, []);
        }
        
        const timeEntries = this.indexes.byTime.get(timeKey);
        timeEntries.push(entry);
        
        // 保持分数排序
        timeEntries.sort((a, b) => b.score - a.score);
    }
    
    /**
     * 添加到分数索引
     * @param {Object} entry - 排行榜条目
     */
    addToScoreIndex(entry) {
        const scoreRange = this.getScoreRange(entry.score);
        
        if (!this.indexes.byScore.has(scoreRange)) {
            this.indexes.byScore.set(scoreRange, []);
        }
        
        const scoreEntries = this.indexes.byScore.get(scoreRange);
        scoreEntries.push(entry);
        
        // 保持分数排序
        scoreEntries.sort((a, b) => b.score - a.score);
    }
    
    /**
     * 添加到复合索引
     * @param {Object} entry - 排行榜条目
     */
    addToCompositeIndex(entry) {
        // 创建常用的复合索引键
        const compositeKeys = [
            `${entry.gameType}_${entry.difficulty}`,
            `${entry.playerId}_${entry.gameType}`,
            `${entry.gameType}_${entry.category}`,
            `${entry.difficulty}_${entry.category}`
        ];
        
        for (const key of compositeKeys) {
            if (!this.indexes.composite.has(key)) {
                this.indexes.composite.set(key, []);
            }
            
            const compositeEntries = this.indexes.composite.get(key);
            compositeEntries.push(entry);
            
            // 保持分数排序
            compositeEntries.sort((a, b) => b.score - a.score);
        }
    }
    
    /**
     * 按主索引查询
     * @param {Object} query - 查询条件
     * @returns {Array} 查询结果
     */
    queryByPrimaryIndex(query) {
        const { gameType, difficulty, category } = query;
        
        const gameIndex = this.indexes.primary.get(gameType);
        if (!gameIndex) return [];
        
        const difficultyIndex = gameIndex.get(difficulty);
        if (!difficultyIndex) return [];
        
        const categoryEntries = difficultyIndex.get(category);
        return categoryEntries ? [...categoryEntries] : [];
    }
    
    /**
     * 按玩家查询
     * @param {Object} query - 查询条件
     * @returns {Array} 查询结果
     */
    queryByPlayer(query) {
        const { playerId } = query;
        const playerEntries = this.indexes.byPlayer.get(playerId);
        
        if (!playerEntries) return [];
        
        // 应用额外过滤条件
        let results = [...playerEntries];
        
        if (query.gameType) {
            results = results.filter(entry => entry.gameType === query.gameType);
        }
        
        if (query.difficulty) {
            results = results.filter(entry => entry.difficulty === query.difficulty);
        }
        
        if (query.category) {
            results = results.filter(entry => entry.category === query.category);
        }
        
        return results;
    }
    
    /**
     * 获取时间键
     * @param {number} timestamp - 时间戳
     * @returns {string} 时间键
     */
    getTimeKey(timestamp) {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }
    
    /**
     * 获取分数范围
     * @param {number} score - 分数
     * @returns {string} 分数范围键
     */
    getScoreRange(score) {
        // 按1000分为一个范围
        const range = Math.floor(score / 1000) * 1000;
        return `${range}-${range + 999}`;
    }
    
    /**
     * 生成缓存键
     * @param {Object} query - 查询条件
     * @returns {string} 缓存键
     */
    generateCacheKey(query) {
        const keyParts = [];
        
        if (query.playerId) keyParts.push(`p:${query.playerId}`);
        if (query.gameType) keyParts.push(`g:${query.gameType}`);
        if (query.difficulty) keyParts.push(`d:${query.difficulty}`);
        if (query.category) keyParts.push(`c:${query.category}`);
        if (query.timeRange) keyParts.push(`t:${query.timeRange}`);
        if (query.limit) keyParts.push(`l:${query.limit}`);
        if (query.offset) keyParts.push(`o:${query.offset}`);
        
        return keyParts.join('_');
    }
    
    /**
     * 从缓存获取数据
     * @param {string} key - 缓存键
     * @returns {*} 缓存数据或null
     */
    getFromCache(key) {
        const cached = this.queryCache.get(key);
        
        if (!cached) return null;
        
        // 检查是否过期
        if (Date.now() - cached.timestamp > this.cacheConfig.ttl) {
            this.queryCache.delete(key);
            return null;
        }
        
        return cached.data;
    }
    
    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {*} data - 缓存数据
     */
    setCache(key, data) {
        // 检查缓存大小限制
        if (this.queryCache.size >= this.cacheConfig.maxSize) {
            this.evictOldestCache();
        }
        
        this.queryCache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }
    
    /**
     * 清除过期缓存
     */
    evictOldestCache() {
        const now = Date.now();
        let oldestKey = null;
        let oldestTime = now;
        
        for (const [key, cached] of this.queryCache.entries()) {
            if (cached.timestamp < oldestTime) {
                oldestTime = cached.timestamp;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.queryCache.delete(oldestKey);
            this.cacheStats.evictions++;
        }
    }
    
    /**
     * 使相关缓存失效
     * @param {Object} entry - 排行榜条目
     */
    invalidateRelatedCache(entry) {
        const keysToRemove = [];
        
        for (const key of this.queryCache.keys()) {
            // 如果缓存键包含相关的游戏类型、难度或玩家ID，则使其失效
            if (key.includes(`g:${entry.gameType}`) ||
                key.includes(`d:${entry.difficulty}`) ||
                key.includes(`p:${entry.playerId}`) ||
                key.includes(`c:${entry.category}`)) {
                keysToRemove.push(key);
            }
        }
        
        for (const key of keysToRemove) {
            this.queryCache.delete(key);
        }
    }
    
    /**
     * 启动缓存清理
     */
    startCacheCleanup() {
        this.cleanupTimer = setInterval(() => {
            this.cleanupExpiredCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    /**
     * 清理过期缓存
     */
    cleanupExpiredCache() {
        const now = Date.now();
        const keysToRemove = [];
        
        for (const [key, cached] of this.queryCache.entries()) {
            if (now - cached.timestamp > this.cacheConfig.ttl) {
                keysToRemove.push(key);
            }
        }
        
        for (const key of keysToRemove) {
            this.queryCache.delete(key);
        }
        
        if (keysToRemove.length > 0) {
            console.log(`🧹 清理了 ${keysToRemove.length} 个过期缓存条目`);
        }
    }
    
    /**
     * 获取索引统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            indexStats: { ...this.indexStats },
            cacheStats: { ...this.cacheStats },
            cacheSize: this.queryCache.size,
            memoryUsage: this.estimateMemoryUsage()
        };
    }
    
    /**
     * 估算内存使用量
     * @returns {number} 估算的内存使用量（字节）
     */
    estimateMemoryUsage() {
        // 简单估算，实际使用量可能有所不同
        let size = 0;
        
        // 估算索引大小
        size += this.indexes.primary.size * 100;
        size += this.indexes.byPlayer.size * 50;
        size += this.indexes.byTime.size * 50;
        size += this.indexes.byScore.size * 50;
        size += this.indexes.composite.size * 50;
        
        // 估算缓存大小
        size += this.queryCache.size * 200;
        
        return size;
    }
    
    /**
     * 销毁索引管理器
     */
    destroy() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        
        // 清空所有索引和缓存
        this.indexes.primary.clear();
        this.indexes.byPlayer.clear();
        this.indexes.byTime.clear();
        this.indexes.byScore.clear();
        this.indexes.composite.clear();
        this.queryCache.clear();
        
        this.initialized = false;
        console.log('🗑️ 排行榜索引管理器已销毁');
    }
}

// 导出索引管理器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LeaderboardIndexManager;
} else if (typeof window !== 'undefined') {
    window.LeaderboardIndexManager = LeaderboardIndexManager;
}
