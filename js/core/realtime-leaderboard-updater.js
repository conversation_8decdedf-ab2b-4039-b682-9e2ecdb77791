/**
 * Split-Second Spark - 实时排行榜更新器
 * 负责排行榜数据的实时更新、排名变化通知和缓存管理
 * 
 * 功能特性:
 * - 实时分数提交和排序
 * - 排名变化检测和通知
 * - 智能缓存更新策略
 * - 事件驱动的更新机制
 * - 批量更新优化
 * - 冲突检测和解决
 */

class RealtimeLeaderboardUpdater {
    constructor() {
        this.initialized = false;
        
        // 依赖组件
        this.leaderboardManager = null;
        this.eventEmitter = null;
        
        // 更新队列
        this.updateQueue = [];
        this.isProcessing = false;
        
        // 配置参数
        this.config = {
            batchSize: 10,              // 批量处理大小
            processInterval: 1000,      // 处理间隔（毫秒）
            maxQueueSize: 1000,         // 最大队列大小
            enableNotifications: true,   // 启用通知
            enableRankTracking: true,    // 启用排名跟踪
            cacheUpdateDelay: 500       // 缓存更新延迟
        };
        
        // 排名跟踪
        this.rankTracker = new Map(); // playerId -> 当前排名信息
        
        // 事件监听器
        this.eventListeners = new Map();
        
        // 统计信息
        this.stats = {
            totalUpdates: 0,
            successfulUpdates: 0,
            failedUpdates: 0,
            notificationsSent: 0,
            averageProcessingTime: 0,
            lastUpdate: null
        };
        
        // 定时器
        this.processTimer = null;
        this.cacheUpdateTimer = null;
        
        console.log('⚡ 实时排行榜更新器已创建');
    }
    
    /**
     * 初始化更新器
     * @param {Object} dependencies - 依赖组件
     */
    async init(dependencies = {}) {
        try {
            // 设置依赖组件
            this.leaderboardManager = dependencies.leaderboardManager || window.enhancedLeaderboardManager;
            this.eventEmitter = dependencies.eventEmitter || this.createSimpleEventEmitter();
            
            if (!this.leaderboardManager) {
                throw new Error('排行榜管理器未提供');
            }
            
            // 启动处理定时器
            this.startProcessing();
            
            // 加载现有排名信息
            await this.loadRankTracker();
            
            this.initialized = true;
            console.log('✅ 实时排行榜更新器初始化完成');
            
        } catch (error) {
            console.error('❌ 实时排行榜更新器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 提交分数更新
     * @param {Object} scoreData - 分数数据
     * @returns {Promise<Object>} 更新结果
     */
    async submitScore(scoreData) {
        try {
            this.validateScoreData(scoreData);
            
            // 创建更新任务
            const updateTask = {
                id: this.generateTaskId(),
                type: 'score_submit',
                data: scoreData,
                timestamp: Date.now(),
                priority: this.calculatePriority(scoreData)
            };
            
            // 添加到队列
            this.addToQueue(updateTask);
            
            console.log(`📊 分数提交已加入队列: ${updateTask.id}`);
            
            // 返回任务ID，可用于跟踪状态
            return {
                taskId: updateTask.id,
                status: 'queued',
                estimatedProcessTime: this.estimateProcessTime()
            };
            
        } catch (error) {
            console.error('❌ 分数提交失败:', error);
            throw error;
        }
    }
    
    /**
     * 批量提交分数
     * @param {Array} scoreDataArray - 分数数据数组
     * @returns {Promise<Array>} 批量更新结果
     */
    async submitBatchScores(scoreDataArray) {
        try {
            if (!Array.isArray(scoreDataArray) || scoreDataArray.length === 0) {
                throw new Error('无效的批量分数数据');
            }
            
            const results = [];
            
            for (const scoreData of scoreDataArray) {
                try {
                    const result = await this.submitScore(scoreData);
                    results.push({ success: true, result: result });
                } catch (error) {
                    results.push({ success: false, error: error.message });
                }
            }
            
            console.log(`📊 批量分数提交完成: ${results.length} 个任务`);
            return results;
            
        } catch (error) {
            console.error('❌ 批量分数提交失败:', error);
            throw error;
        }
    }
    
    /**
     * 监听排名变化事件
     * @param {string} playerId - 玩家ID
     * @param {Function} callback - 回调函数
     */
    onRankChange(playerId, callback) {
        const eventKey = `rank_change_${playerId}`;
        
        if (!this.eventListeners.has(eventKey)) {
            this.eventListeners.set(eventKey, []);
        }
        
        this.eventListeners.get(eventKey).push(callback);
        
        console.log(`👂 已注册排名变化监听器: ${playerId}`);
    }
    
    /**
     * 移除排名变化监听器
     * @param {string} playerId - 玩家ID
     * @param {Function} callback - 回调函数
     */
    offRankChange(playerId, callback) {
        const eventKey = `rank_change_${playerId}`;
        const listeners = this.eventListeners.get(eventKey);
        
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
            
            if (listeners.length === 0) {
                this.eventListeners.delete(eventKey);
            }
        }
    }
    
    /**
     * 获取实时排名信息
     * @param {string} playerId - 玩家ID
     * @param {Object} options - 选项
     * @returns {Object} 排名信息
     */
    getRealtimeRank(playerId, options = {}) {
        const rankInfo = this.rankTracker.get(playerId);
        
        if (!rankInfo) {
            return {
                playerId: playerId,
                hasData: false,
                message: '暂无排名数据'
            };
        }
        
        // 过滤指定的游戏类型和难度
        let filteredRanks = rankInfo.ranks;
        
        if (options.gameType) {
            filteredRanks = filteredRanks.filter(rank => rank.gameType === options.gameType);
        }
        
        if (options.difficulty) {
            filteredRanks = filteredRanks.filter(rank => rank.difficulty === options.difficulty);
        }
        
        return {
            playerId: playerId,
            hasData: true,
            ranks: filteredRanks,
            lastUpdate: rankInfo.lastUpdate,
            totalRanks: filteredRanks.length
        };
    }
    
    /**
     * 强制刷新排名
     * @param {string} playerId - 玩家ID（可选，不提供则刷新所有）
     */
    async forceRefreshRanks(playerId = null) {
        try {
            if (playerId) {
                await this.refreshPlayerRank(playerId);
                console.log(`🔄 已刷新玩家排名: ${playerId}`);
            } else {
                await this.refreshAllRanks();
                console.log('🔄 已刷新所有玩家排名');
            }
        } catch (error) {
            console.error('❌ 刷新排名失败:', error);
            throw error;
        }
    }
    
    /**
     * 添加任务到队列
     * @param {Object} task - 更新任务
     */
    addToQueue(task) {
        // 检查队列大小限制
        if (this.updateQueue.length >= this.config.maxQueueSize) {
            console.warn('⚠️ 更新队列已满，移除最旧的任务');
            this.updateQueue.shift();
        }
        
        // 按优先级插入
        const insertIndex = this.findInsertPosition(task);
        this.updateQueue.splice(insertIndex, 0, task);
    }
    
    /**
     * 查找插入位置（按优先级排序）
     * @param {Object} task - 任务
     * @returns {number} 插入位置
     */
    findInsertPosition(task) {
        for (let i = 0; i < this.updateQueue.length; i++) {
            if (task.priority > this.updateQueue[i].priority) {
                return i;
            }
        }
        return this.updateQueue.length;
    }
    
    /**
     * 启动处理循环
     */
    startProcessing() {
        this.processTimer = setInterval(async () => {
            if (!this.isProcessing && this.updateQueue.length > 0) {
                await this.processQueue();
            }
        }, this.config.processInterval);
        
        console.log('🔄 实时更新处理器已启动');
    }
    
    /**
     * 处理更新队列
     */
    async processQueue() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        const startTime = Date.now();
        
        try {
            // 取出一批任务进行处理
            const batch = this.updateQueue.splice(0, this.config.batchSize);
            
            if (batch.length === 0) {
                this.isProcessing = false;
                return;
            }
            
            console.log(`⚡ 开始处理 ${batch.length} 个更新任务`);
            
            // 并行处理任务
            const results = await Promise.allSettled(
                batch.map(task => this.processTask(task))
            );
            
            // 统计结果
            let successful = 0;
            let failed = 0;
            
            for (const result of results) {
                if (result.status === 'fulfilled') {
                    successful++;
                } else {
                    failed++;
                    console.error('❌ 任务处理失败:', result.reason);
                }
            }
            
            // 更新统计信息
            this.stats.totalUpdates += batch.length;
            this.stats.successfulUpdates += successful;
            this.stats.failedUpdates += failed;
            
            const processingTime = Date.now() - startTime;
            this.stats.averageProcessingTime = 
                (this.stats.averageProcessingTime + processingTime) / 2;
            this.stats.lastUpdate = Date.now();
            
            console.log(`✅ 批量处理完成: ${successful} 成功, ${failed} 失败, 耗时 ${processingTime}ms`);
            
        } catch (error) {
            console.error('❌ 队列处理失败:', error);
        } finally {
            this.isProcessing = false;
        }
    }
    
    /**
     * 处理单个任务
     * @param {Object} task - 更新任务
     */
    async processTask(task) {
        switch (task.type) {
            case 'score_submit':
                return await this.processScoreSubmit(task);
            case 'rank_refresh':
                return await this.processRankRefresh(task);
            default:
                throw new Error(`未知的任务类型: ${task.type}`);
        }
    }
    
    /**
     * 处理分数提交任务
     * @param {Object} task - 分数提交任务
     */
    async processScoreSubmit(task) {
        const { data } = task;
        
        // 获取提交前的排名
        const oldRank = await this.getPlayerCurrentRank(data.playerId, data.gameType, data.difficulty, data.category);
        
        // 创建排行榜条目
        const entry = await this.leaderboardManager.createEntry(data);
        
        // 获取提交后的排名
        const newRank = await this.getPlayerCurrentRank(data.playerId, data.gameType, data.difficulty, data.category);
        
        // 更新排名跟踪器
        await this.updateRankTracker(data.playerId, data.gameType, data.difficulty, data.category, newRank);
        
        // 检查排名变化并发送通知
        if (this.config.enableNotifications && oldRank !== newRank) {
            await this.sendRankChangeNotification(data.playerId, {
                gameType: data.gameType,
                difficulty: data.difficulty,
                category: data.category,
                oldRank: oldRank,
                newRank: newRank,
                score: data.score,
                entry: entry
            });
        }
        
        return {
            taskId: task.id,
            entryId: entry.entryId,
            oldRank: oldRank,
            newRank: newRank,
            rankChanged: oldRank !== newRank
        };
    }
    
    /**
     * 获取玩家当前排名
     * @param {string} playerId - 玩家ID
     * @param {string} gameType - 游戏类型
     * @param {string} difficulty - 难度
     * @param {string} category - 类别
     * @returns {number} 当前排名
     */
    async getPlayerCurrentRank(playerId, gameType, difficulty, category) {
        try {
            const leaderboard = await this.leaderboardManager.query({
                gameType: gameType,
                difficulty: difficulty,
                category: category,
                limit: 1000
            });
            
            const playerIndex = leaderboard.findIndex(entry => entry.playerId === playerId);
            return playerIndex >= 0 ? playerIndex + 1 : -1;
            
        } catch (error) {
            console.error('❌ 获取玩家排名失败:', error);
            return -1;
        }
    }
    
    /**
     * 更新排名跟踪器
     * @param {string} playerId - 玩家ID
     * @param {string} gameType - 游戏类型
     * @param {string} difficulty - 难度
     * @param {string} category - 类别
     * @param {number} rank - 排名
     */
    async updateRankTracker(playerId, gameType, difficulty, category, rank) {
        if (!this.rankTracker.has(playerId)) {
            this.rankTracker.set(playerId, {
                playerId: playerId,
                ranks: [],
                lastUpdate: Date.now()
            });
        }
        
        const playerRankInfo = this.rankTracker.get(playerId);
        
        // 查找或创建对应的排名记录
        const rankKey = `${gameType}_${difficulty}_${category}`;
        let rankRecord = playerRankInfo.ranks.find(r => 
            r.gameType === gameType && r.difficulty === difficulty && r.category === category
        );
        
        if (!rankRecord) {
            rankRecord = {
                gameType: gameType,
                difficulty: difficulty,
                category: category,
                rank: rank,
                lastUpdate: Date.now(),
                history: []
            };
            playerRankInfo.ranks.push(rankRecord);
        } else {
            // 保存历史排名
            rankRecord.history.push({
                rank: rankRecord.rank,
                timestamp: rankRecord.lastUpdate
            });
            
            // 限制历史记录数量
            if (rankRecord.history.length > 10) {
                rankRecord.history.shift();
            }
            
            rankRecord.rank = rank;
            rankRecord.lastUpdate = Date.now();
        }
        
        playerRankInfo.lastUpdate = Date.now();
    }
    
    /**
     * 发送排名变化通知
     * @param {string} playerId - 玩家ID
     * @param {Object} changeInfo - 变化信息
     */
    async sendRankChangeNotification(playerId, changeInfo) {
        try {
            const eventKey = `rank_change_${playerId}`;
            const listeners = this.eventListeners.get(eventKey);
            
            if (listeners && listeners.length > 0) {
                const notification = {
                    playerId: playerId,
                    timestamp: Date.now(),
                    ...changeInfo
                };
                
                // 通知所有监听器
                for (const listener of listeners) {
                    try {
                        await listener(notification);
                    } catch (error) {
                        console.error('❌ 通知监听器失败:', error);
                    }
                }
                
                this.stats.notificationsSent++;
                console.log(`📢 排名变化通知已发送: ${playerId} (${changeInfo.oldRank} → ${changeInfo.newRank})`);
            }
            
        } catch (error) {
            console.error('❌ 发送排名变化通知失败:', error);
        }
    }
    
    /**
     * 验证分数数据
     * @param {Object} scoreData - 分数数据
     */
    validateScoreData(scoreData) {
        const required = ['playerId', 'playerName', 'gameType', 'difficulty', 'category', 'score'];
        
        for (const field of required) {
            if (!scoreData[field] && scoreData[field] !== 0) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }
        
        if (typeof scoreData.score !== 'number' || scoreData.score < 0) {
            throw new Error(`无效的分数: ${scoreData.score}`);
        }
    }
    
    /**
     * 计算任务优先级
     * @param {Object} scoreData - 分数数据
     * @returns {number} 优先级（数字越大优先级越高）
     */
    calculatePriority(scoreData) {
        let priority = 0;
        
        // 高分数的更新优先级更高
        priority += Math.min(scoreData.score / 1000, 10);
        
        // 困难难度的更新优先级更高
        const difficultyPriority = {
            'beginner': 1,
            'easy': 2,
            'normal': 3,
            'hard': 4,
            'expert': 5,
            'master': 6
        };
        priority += difficultyPriority[scoreData.difficulty] || 0;
        
        return priority;
    }
    
    /**
     * 估算处理时间
     * @returns {number} 估算的处理时间（毫秒）
     */
    estimateProcessTime() {
        const queuePosition = this.updateQueue.length;
        const avgProcessTime = this.stats.averageProcessingTime || 1000;
        
        return Math.ceil(queuePosition / this.config.batchSize) * avgProcessTime;
    }
    
    /**
     * 生成任务ID
     * @returns {string} 任务ID
     */
    generateTaskId() {
        return `task_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    }
    
    /**
     * 创建简单的事件发射器
     * @returns {Object} 事件发射器
     */
    createSimpleEventEmitter() {
        return {
            emit: (event, data) => {
                console.log(`📡 事件发射: ${event}`, data);
            },
            on: (event, callback) => {
                console.log(`👂 事件监听: ${event}`);
            }
        };
    }
    
    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            queueSize: this.updateQueue.length,
            isProcessing: this.isProcessing,
            trackedPlayers: this.rankTracker.size
        };
    }
    
    /**
     * 销毁更新器
     */
    destroy() {
        if (this.processTimer) {
            clearInterval(this.processTimer);
            this.processTimer = null;
        }
        
        if (this.cacheUpdateTimer) {
            clearInterval(this.cacheUpdateTimer);
            this.cacheUpdateTimer = null;
        }
        
        this.updateQueue.length = 0;
        this.rankTracker.clear();
        this.eventListeners.clear();
        this.initialized = false;
        
        console.log('🗑️ 实时排行榜更新器已销毁');
    }
}

// 导出实时排行榜更新器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealtimeLeaderboardUpdater;
} else if (typeof window !== 'undefined') {
    window.RealtimeLeaderboardUpdater = RealtimeLeaderboardUpdater;
}
