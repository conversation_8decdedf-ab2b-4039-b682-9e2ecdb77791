/**
 * Split-Second Spark - 排行榜数据分析器
 * 提供全面的排行榜数据统计分析功能
 * 
 * 功能特性:
 * - 趋势分析和预测
 * - 玩家表现对比
 * - 难度分布统计
 * - 游戏平衡性分析
 * - 用户行为洞察
 * - 数据可视化支持
 */

class LeaderboardAnalytics {
    constructor() {
        this.initialized = false;
        
        // 依赖组件
        this.leaderboardManager = null;
        
        // 分析配置
        this.config = {
            trendAnalysisPeriod: 30,        // 趋势分析周期（天）
            sampleSize: 1000,               // 分析样本大小
            confidenceLevel: 0.95,          // 置信水平
            outlierThreshold: 2.5,          // 异常值阈值（标准差倍数）
            minDataPoints: 10               // 最小数据点数量
        };
        
        // 缓存分析结果
        this.analysisCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
        
        // 分析统计
        this.stats = {
            totalAnalyses: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageAnalysisTime: 0,
            lastAnalysis: null
        };
        
        console.log('📊 排行榜数据分析器已创建');
    }
    
    /**
     * 初始化分析器
     * @param {Object} dependencies - 依赖组件
     */
    async init(dependencies = {}) {
        try {
            this.leaderboardManager = dependencies.leaderboardManager || window.enhancedLeaderboardManager;
            
            if (!this.leaderboardManager) {
                throw new Error('排行榜管理器未提供');
            }
            
            this.initialized = true;
            console.log('✅ 排行榜数据分析器初始化完成');
            
        } catch (error) {
            console.error('❌ 排行榜数据分析器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 生成综合分析报告
     * @param {Object} options - 分析选项
     * @returns {Promise<Object>} 分析报告
     */
    async generateComprehensiveReport(options = {}) {
        const startTime = Date.now();
        
        try {
            this.validateInitialized();
            
            const cacheKey = this.generateCacheKey('comprehensive', options);
            const cached = this.getFromCache(cacheKey);
            
            if (cached) {
                this.stats.cacheHits++;
                return cached;
            }
            
            this.stats.cacheMisses++;
            
            console.log('📊 开始生成综合分析报告...');
            
            // 并行执行各种分析
            const [
                overviewStats,
                trendAnalysis,
                difficultyAnalysis,
                playerAnalysis,
                gameBalanceAnalysis,
                performanceMetrics
            ] = await Promise.all([
                this.generateOverviewStatistics(options),
                this.analyzeTrends(options),
                this.analyzeDifficultyDistribution(options),
                this.analyzePlayerBehavior(options),
                this.analyzeGameBalance(options),
                this.analyzePerformanceMetrics(options)
            ]);
            
            const report = {
                metadata: {
                    generatedAt: Date.now(),
                    analysisTime: Date.now() - startTime,
                    dataRange: options.dateRange || 'all_time',
                    sampleSize: options.sampleSize || this.config.sampleSize,
                    version: '1.0.0'
                },
                overview: overviewStats,
                trends: trendAnalysis,
                difficulty: difficultyAnalysis,
                players: playerAnalysis,
                gameBalance: gameBalanceAnalysis,
                performance: performanceMetrics,
                insights: this.generateInsights({
                    overviewStats,
                    trendAnalysis,
                    difficultyAnalysis,
                    playerAnalysis,
                    gameBalanceAnalysis
                }),
                recommendations: this.generateRecommendations({
                    overviewStats,
                    trendAnalysis,
                    difficultyAnalysis,
                    playerAnalysis,
                    gameBalanceAnalysis
                })
            };
            
            // 缓存结果
            this.setCache(cacheKey, report);
            
            // 更新统计
            this.updateAnalysisStats(Date.now() - startTime);
            
            console.log(`✅ 综合分析报告生成完成 (${Date.now() - startTime}ms)`);
            return report;
            
        } catch (error) {
            console.error('❌ 生成综合分析报告失败:', error);
            throw error;
        }
    }
    
    /**
     * 生成概览统计
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 概览统计
     */
    async generateOverviewStatistics(options = {}) {
        try {
            // 获取所有排行榜数据
            const allData = await this.getAllLeaderboardData(options);
            
            const stats = {
                totalEntries: allData.length,
                uniquePlayers: new Set(allData.map(entry => entry.playerId)).size,
                gameTypes: this.countByField(allData, 'gameType'),
                difficulties: this.countByField(allData, 'difficulty'),
                categories: this.countByField(allData, 'category'),
                timeRange: {
                    earliest: allData.length > 0 ? Math.min(...allData.map(e => e.timestamp)) : null,
                    latest: allData.length > 0 ? Math.max(...allData.map(e => e.timestamp)) : null
                },
                scoreStatistics: this.calculateScoreStatistics(allData),
                activityByPeriod: this.analyzeActivityByPeriod(allData)
            };
            
            return stats;
            
        } catch (error) {
            console.error('❌ 生成概览统计失败:', error);
            return {};
        }
    }
    
    /**
     * 分析趋势
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 趋势分析结果
     */
    async analyzeTrends(options = {}) {
        try {
            const period = options.period || this.config.trendAnalysisPeriod;
            const endDate = new Date();
            const startDate = new Date(endDate.getTime() - period * 24 * 60 * 60 * 1000);
            
            const data = await this.getLeaderboardDataByDateRange(startDate, endDate, options);
            
            if (data.length < this.config.minDataPoints) {
                return {
                    hasEnoughData: false,
                    message: '数据点不足，无法进行趋势分析',
                    requiredPoints: this.config.minDataPoints,
                    actualPoints: data.length
                };
            }
            
            // 按日期分组数据
            const dailyData = this.groupDataByDay(data);
            
            const trends = {
                hasEnoughData: true,
                period: period,
                dailySubmissions: this.calculateDailyTrend(dailyData, 'count'),
                dailyAverageScore: this.calculateDailyTrend(dailyData, 'averageScore'),
                dailyTopScore: this.calculateDailyTrend(dailyData, 'topScore'),
                playerGrowth: this.calculatePlayerGrowthTrend(dailyData),
                difficultyTrends: this.calculateDifficultyTrends(dailyData),
                predictions: this.generateTrendPredictions(dailyData)
            };
            
            return trends;
            
        } catch (error) {
            console.error('❌ 趋势分析失败:', error);
            return { hasEnoughData: false, error: error.message };
        }
    }
    
    /**
     * 分析难度分布
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 难度分析结果
     */
    async analyzeDifficultyDistribution(options = {}) {
        try {
            const data = await this.getAllLeaderboardData(options);
            
            const difficultyStats = {};
            const difficulties = ['beginner', 'easy', 'normal', 'hard', 'expert', 'master'];
            
            for (const difficulty of difficulties) {
                const difficultyData = data.filter(entry => entry.difficulty === difficulty);
                
                difficultyStats[difficulty] = {
                    totalEntries: difficultyData.length,
                    uniquePlayers: new Set(difficultyData.map(e => e.playerId)).size,
                    averageScore: this.calculateAverage(difficultyData.map(e => e.score)),
                    medianScore: this.calculateMedian(difficultyData.map(e => e.score)),
                    scoreRange: {
                        min: difficultyData.length > 0 ? Math.min(...difficultyData.map(e => e.score)) : 0,
                        max: difficultyData.length > 0 ? Math.max(...difficultyData.map(e => e.score)) : 0
                    },
                    playerRetention: this.calculatePlayerRetention(difficultyData),
                    progressionRate: this.calculateProgressionRate(difficultyData, difficulty)
                };
            }
            
            return {
                byDifficulty: difficultyStats,
                distribution: this.calculateDifficultyDistribution(data),
                balanceScore: this.calculateDifficultyBalance(difficultyStats),
                recommendations: this.generateDifficultyRecommendations(difficultyStats)
            };
            
        } catch (error) {
            console.error('❌ 难度分析失败:', error);
            return {};
        }
    }
    
    /**
     * 分析玩家行为
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 玩家行为分析结果
     */
    async analyzePlayerBehavior(options = {}) {
        try {
            const data = await this.getAllLeaderboardData(options);
            
            // 按玩家分组
            const playerGroups = this.groupDataByPlayer(data);
            
            const behaviorStats = {
                playerSegments: this.segmentPlayers(playerGroups),
                engagementMetrics: this.calculateEngagementMetrics(playerGroups),
                retentionAnalysis: this.analyzePlayerRetention(playerGroups),
                skillProgression: this.analyzeSkillProgression(playerGroups),
                preferenceAnalysis: this.analyzePlayerPreferences(playerGroups),
                churnRisk: this.identifyChurnRisk(playerGroups)
            };
            
            return behaviorStats;
            
        } catch (error) {
            console.error('❌ 玩家行为分析失败:', error);
            return {};
        }
    }
    
    /**
     * 分析游戏平衡性
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 游戏平衡分析结果
     */
    async analyzeGameBalance(options = {}) {
        try {
            const data = await this.getAllLeaderboardData(options);
            
            const balanceAnalysis = {
                scoreDistribution: this.analyzeScoreDistribution(data),
                difficultyBalance: this.analyzeDifficultyBalance(data),
                gameTypeBalance: this.analyzeGameTypeBalance(data),
                outlierDetection: this.detectOutliers(data),
                fairnessMetrics: this.calculateFairnessMetrics(data),
                competitiveBalance: this.analyzeCompetitiveBalance(data)
            };
            
            return balanceAnalysis;
            
        } catch (error) {
            console.error('❌ 游戏平衡分析失败:', error);
            return {};
        }
    }
    
    /**
     * 分析性能指标
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 性能指标分析结果
     */
    async analyzePerformanceMetrics(options = {}) {
        try {
            const data = await this.getAllLeaderboardData(options);
            
            const performanceMetrics = {
                systemPerformance: {
                    averageGameDuration: this.calculateAverageGameDuration(data),
                    completionRates: this.calculateCompletionRates(data),
                    errorRates: this.calculateErrorRates(data)
                },
                userExperience: {
                    satisfactionScore: this.calculateSatisfactionScore(data),
                    difficultyRating: this.calculateDifficultyRating(data),
                    replayability: this.calculateReplayability(data)
                },
                technicalMetrics: {
                    dataQuality: this.assessDataQuality(data),
                    consistencyScore: this.calculateConsistencyScore(data),
                    reliabilityMetrics: this.calculateReliabilityMetrics(data)
                }
            };
            
            return performanceMetrics;
            
        } catch (error) {
            console.error('❌ 性能指标分析失败:', error);
            return {};
        }
    }
    
    /**
     * 生成洞察
     * @param {Object} analysisResults - 分析结果
     * @returns {Array} 洞察列表
     */
    generateInsights(analysisResults) {
        const insights = [];
        
        try {
            const { overviewStats, trendAnalysis, difficultyAnalysis, playerAnalysis } = analysisResults;
            
            // 参与度洞察
            if (overviewStats.uniquePlayers && overviewStats.totalEntries) {
                const avgSubmissionsPerPlayer = overviewStats.totalEntries / overviewStats.uniquePlayers;
                if (avgSubmissionsPerPlayer > 10) {
                    insights.push({
                        type: 'positive',
                        category: 'engagement',
                        title: '高玩家参与度',
                        description: `平均每位玩家提交 ${avgSubmissionsPerPlayer.toFixed(1)} 次分数，显示出良好的游戏粘性`,
                        impact: 'high',
                        confidence: 0.9
                    });
                }
            }
            
            // 难度平衡洞察
            if (difficultyAnalysis.balanceScore) {
                if (difficultyAnalysis.balanceScore > 0.8) {
                    insights.push({
                        type: 'positive',
                        category: 'balance',
                        title: '难度设计平衡',
                        description: '各难度等级的玩家分布相对均衡，难度曲线设计合理',
                        impact: 'medium',
                        confidence: 0.85
                    });
                } else if (difficultyAnalysis.balanceScore < 0.5) {
                    insights.push({
                        type: 'warning',
                        category: 'balance',
                        title: '难度分布不均',
                        description: '某些难度等级的玩家过于集中，建议调整难度设计',
                        impact: 'high',
                        confidence: 0.8
                    });
                }
            }
            
            // 趋势洞察
            if (trendAnalysis.hasEnoughData && trendAnalysis.playerGrowth) {
                const growthRate = trendAnalysis.playerGrowth.rate;
                if (growthRate > 0.1) {
                    insights.push({
                        type: 'positive',
                        category: 'growth',
                        title: '玩家增长强劲',
                        description: `玩家数量呈现 ${(growthRate * 100).toFixed(1)}% 的增长趋势`,
                        impact: 'high',
                        confidence: 0.9
                    });
                } else if (growthRate < -0.05) {
                    insights.push({
                        type: 'warning',
                        category: 'growth',
                        title: '玩家流失风险',
                        description: '玩家数量出现下降趋势，需要关注用户留存',
                        impact: 'high',
                        confidence: 0.8
                    });
                }
            }
            
            // 性能洞察
            if (playerAnalysis.engagementMetrics) {
                const avgSessionTime = playerAnalysis.engagementMetrics.averageSessionTime;
                if (avgSessionTime > 300000) { // 5分钟
                    insights.push({
                        type: 'positive',
                        category: 'engagement',
                        title: '良好的游戏时长',
                        description: `平均游戏时长 ${(avgSessionTime / 60000).toFixed(1)} 分钟，用户参与度较高`,
                        impact: 'medium',
                        confidence: 0.8
                    });
                }
            }
            
        } catch (error) {
            console.error('❌ 生成洞察失败:', error);
        }
        
        return insights;
    }
    
    /**
     * 生成建议
     * @param {Object} analysisResults - 分析结果
     * @returns {Array} 建议列表
     */
    generateRecommendations(analysisResults) {
        const recommendations = [];
        
        try {
            const { overviewStats, difficultyAnalysis, playerAnalysis, gameBalanceAnalysis } = analysisResults;
            
            // 难度调整建议
            if (difficultyAnalysis.balanceScore && difficultyAnalysis.balanceScore < 0.6) {
                recommendations.push({
                    category: 'game_design',
                    priority: 'high',
                    title: '优化难度平衡',
                    description: '调整难度设置，使各难度等级的玩家分布更加均衡',
                    actions: [
                        '分析玩家在各难度的表现数据',
                        '调整过于简单或困难的关卡',
                        '增加渐进式难度过渡',
                        '提供更多难度选择'
                    ],
                    expectedImpact: '提升玩家留存率和满意度'
                });
            }
            
            // 参与度提升建议
            if (overviewStats.uniquePlayers && overviewStats.totalEntries) {
                const avgSubmissions = overviewStats.totalEntries / overviewStats.uniquePlayers;
                if (avgSubmissions < 5) {
                    recommendations.push({
                        category: 'engagement',
                        priority: 'medium',
                        title: '提升玩家参与度',
                        description: '增加激励机制，鼓励玩家更频繁地参与游戏',
                        actions: [
                            '添加每日挑战任务',
                            '设计成就系统',
                            '增加社交功能',
                            '优化奖励机制'
                        ],
                        expectedImpact: '增加玩家活跃度和游戏时长'
                    });
                }
            }
            
            // 数据质量建议
            if (gameBalanceAnalysis.outlierDetection && gameBalanceAnalysis.outlierDetection.count > 0) {
                recommendations.push({
                    category: 'data_quality',
                    priority: 'high',
                    title: '加强数据验证',
                    description: '检测到异常数据，建议加强防作弊和数据验证机制',
                    actions: [
                        '完善反作弊系统',
                        '增加数据验证规则',
                        '实施实时监控',
                        '建立异常数据处理流程'
                    ],
                    expectedImpact: '提高排行榜公平性和可信度'
                });
            }
            
            // 用户体验建议
            if (playerAnalysis.churnRisk && playerAnalysis.churnRisk.highRiskPlayers > 0) {
                recommendations.push({
                    category: 'user_experience',
                    priority: 'medium',
                    title: '降低用户流失',
                    description: '识别到高流失风险用户，建议实施留存策略',
                    actions: [
                        '个性化推荐系统',
                        '新手引导优化',
                        '客服支持改进',
                        '用户反馈收集'
                    ],
                    expectedImpact: '降低用户流失率，提高长期价值'
                });
            }
            
        } catch (error) {
            console.error('❌ 生成建议失败:', error);
        }
        
        return recommendations;
    }
    
    /**
     * 获取所有排行榜数据
     * @param {Object} options - 选项
     * @returns {Promise<Array>} 排行榜数据
     */
    async getAllLeaderboardData(options = {}) {
        try {
            const query = {
                limit: options.sampleSize || this.config.sampleSize,
                ...options.filters
            };
            
            return await this.leaderboardManager.query(query);
            
        } catch (error) {
            console.error('❌ 获取排行榜数据失败:', error);
            return [];
        }
    }
    
    /**
     * 按字段统计数量
     * @param {Array} data - 数据数组
     * @param {string} field - 字段名
     * @returns {Object} 统计结果
     */
    countByField(data, field) {
        const counts = {};
        for (const item of data) {
            const value = item[field];
            counts[value] = (counts[value] || 0) + 1;
        }
        return counts;
    }
    
    /**
     * 计算分数统计
     * @param {Array} data - 数据数组
     * @returns {Object} 分数统计
     */
    calculateScoreStatistics(data) {
        if (data.length === 0) return {};
        
        const scores = data.map(entry => entry.score);
        
        return {
            min: Math.min(...scores),
            max: Math.max(...scores),
            average: this.calculateAverage(scores),
            median: this.calculateMedian(scores),
            standardDeviation: this.calculateStandardDeviation(scores),
            percentiles: this.calculatePercentiles(scores, [25, 50, 75, 90, 95, 99])
        };
    }
    
    /**
     * 计算平均值
     * @param {Array} values - 数值数组
     * @returns {number} 平均值
     */
    calculateAverage(values) {
        if (values.length === 0) return 0;
        return values.reduce((sum, value) => sum + value, 0) / values.length;
    }
    
    /**
     * 计算中位数
     * @param {Array} values - 数值数组
     * @returns {number} 中位数
     */
    calculateMedian(values) {
        if (values.length === 0) return 0;
        
        const sorted = [...values].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        
        return sorted.length % 2 === 0 
            ? (sorted[mid - 1] + sorted[mid]) / 2 
            : sorted[mid];
    }
    
    /**
     * 计算标准差
     * @param {Array} values - 数值数组
     * @returns {number} 标准差
     */
    calculateStandardDeviation(values) {
        if (values.length === 0) return 0;
        
        const avg = this.calculateAverage(values);
        const squaredDiffs = values.map(value => Math.pow(value - avg, 2));
        const avgSquaredDiff = this.calculateAverage(squaredDiffs);
        
        return Math.sqrt(avgSquaredDiff);
    }
    
    /**
     * 计算百分位数
     * @param {Array} values - 数值数组
     * @param {Array} percentiles - 百分位数数组
     * @returns {Object} 百分位数结果
     */
    calculatePercentiles(values, percentiles) {
        if (values.length === 0) return {};
        
        const sorted = [...values].sort((a, b) => a - b);
        const result = {};
        
        for (const p of percentiles) {
            const index = Math.ceil((p / 100) * sorted.length) - 1;
            result[`p${p}`] = sorted[Math.max(0, index)];
        }
        
        return result;
    }
    
    /**
     * 生成缓存键
     * @param {string} type - 分析类型
     * @param {Object} options - 选项
     * @returns {string} 缓存键
     */
    generateCacheKey(type, options) {
        const keyParts = [type];
        
        if (options.dateRange) keyParts.push(`date:${options.dateRange}`);
        if (options.gameType) keyParts.push(`game:${options.gameType}`);
        if (options.difficulty) keyParts.push(`diff:${options.difficulty}`);
        if (options.sampleSize) keyParts.push(`size:${options.sampleSize}`);
        
        return keyParts.join('_');
    }
    
    /**
     * 从缓存获取数据
     * @param {string} key - 缓存键
     * @returns {*} 缓存数据或null
     */
    getFromCache(key) {
        const cached = this.analysisCache.get(key);
        
        if (!cached) return null;
        
        if (Date.now() - cached.timestamp > this.cacheExpiry) {
            this.analysisCache.delete(key);
            return null;
        }
        
        return cached.data;
    }
    
    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {*} data - 缓存数据
     */
    setCache(key, data) {
        this.analysisCache.set(key, {
            data: data,
            timestamp: Date.now()
        });
        
        // 限制缓存大小
        if (this.analysisCache.size > 100) {
            const oldestKey = this.analysisCache.keys().next().value;
            this.analysisCache.delete(oldestKey);
        }
    }
    
    /**
     * 验证是否已初始化
     */
    validateInitialized() {
        if (!this.initialized) {
            throw new Error('排行榜数据分析器未初始化');
        }
    }
    
    /**
     * 更新分析统计
     * @param {number} analysisTime - 分析时间
     */
    updateAnalysisStats(analysisTime) {
        this.stats.totalAnalyses++;
        this.stats.averageAnalysisTime = 
            (this.stats.averageAnalysisTime + analysisTime) / 2;
        this.stats.lastAnalysis = {
            timestamp: Date.now(),
            duration: analysisTime
        };
    }
    
    /**
     * 获取分析统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.analysisCache.size,
            cacheHitRate: this.stats.totalAnalyses > 0 ? 
                (this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * 清理缓存
     */
    clearCache() {
        this.analysisCache.clear();
        console.log('🧹 分析缓存已清理');
    }
}

// 导出排行榜数据分析器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LeaderboardAnalytics;
} else if (typeof window !== 'undefined') {
    window.LeaderboardAnalytics = LeaderboardAnalytics;
}
