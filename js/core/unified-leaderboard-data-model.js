/**
 * Split-Second Spark - 统一排行榜数据模型
 * 为所有游戏提供统一的排行榜数据结构和管理接口
 * 
 * 功能特性:
 * - 多维度数据支持（玩家ID、难度等级、游戏类型、时间范围）
 * - 统一的数据验证和序列化
 * - 高效的索引和查询策略
 * - 可扩展的指标类型系统
 * - 数据完整性保证
 */

class UnifiedLeaderboardDataModel {
    constructor() {
        // 数据模型版本，用于数据迁移和兼容性
        this.version = '1.0.0';
        
        // 支持的游戏类型
        this.gameTypes = {
            SPARK_CATCHER: 'spark-catcher',      // 瞬光捕手
            QUANTUM_RESONATOR: 'quantum-resonator', // 量子共鸣者
            TEMPORAL_WEAVER: 'temporal-weaver'    // 时空织梦者
        };
        
        // 难度等级定义（统一所有游戏）
        this.difficultyLevels = {
            BEGINNER: 'beginner',   // 初学者 (0.6x)
            EASY: 'easy',          // 简单 (0.8x)
            NORMAL: 'normal',      // 普通 (1.0x)
            HARD: 'hard',          // 困难 (1.3x)
            EXPERT: 'expert',      // 专家 (1.6x)
            MASTER: 'master'       // 大师 (2.0x)
        };
        
        // 难度权重配置（用于跨难度比较）
        this.difficultyWeights = {
            beginner: 0.6,
            easy: 0.8,
            normal: 1.0,
            hard: 1.3,
            expert: 1.6,
            master: 2.0
        };
        
        // 排行榜类别定义
        this.leaderboardCategories = {
            // 通用类别（所有游戏都支持）
            HIGH_SCORE: 'high_score',                    // 最高分数
            DAILY_HIGH_SCORE: 'daily_high_score',        // 每日最高分
            WEEKLY_HIGH_SCORE: 'weekly_high_score',      // 每周最高分
            MONTHLY_HIGH_SCORE: 'monthly_high_score',    // 每月最高分
            
            // 技能类别
            PERFECT_HITS: 'perfect_hits',                // 完美击中数
            COMBO_RECORD: 'combo_record',                // 最高连击
            ACCURACY_RATE: 'accuracy_rate',              // 准确率
            
            // 时间类别
            FASTEST_COMPLETION: 'fastest_completion',     // 最快通关
            LONGEST_SURVIVAL: 'longest_survival',         // 最长生存
            
            // 特殊类别
            LEVEL_COMPLETION: 'level_completion',         // 关卡完成度
            CUSTOM_LEVEL_SCORE: 'custom_level_score'      // 自定义关卡分数
        };
        
        // 时间范围类型
        this.timeRangeTypes = {
            ALL_TIME: 'all_time',      // 历史总榜
            DAILY: 'daily',            // 每日榜
            WEEKLY: 'weekly',          // 每周榜
            MONTHLY: 'monthly',        // 每月榜
            SEASONAL: 'seasonal'       // 赛季榜
        };
        
        // 排行榜条目状态
        this.entryStatus = {
            ACTIVE: 'active',          // 活跃状态
            ARCHIVED: 'archived',      // 已归档
            FLAGGED: 'flagged',        // 被标记（可疑）
            VERIFIED: 'verified'       // 已验证
        };
        
        console.log('🏗️ 统一排行榜数据模型已初始化');
    }
    
    /**
     * 创建标准排行榜条目
     * @param {Object} params - 参数对象
     * @returns {Object} 标准化的排行榜条目
     */
    createLeaderboardEntry(params) {
        const {
            playerId,
            playerName,
            gameType,
            difficulty,
            category,
            score,
            metadata = {}
        } = params;
        
        // 验证必需参数
        this.validateRequiredParams(params);
        
        // 生成唯一条目ID
        const entryId = this.generateEntryId(playerId, gameType, difficulty, category);
        
        // 创建基础条目结构
        const entry = {
            // 基础标识信息
            entryId: entryId,
            playerId: playerId,
            playerName: playerName,
            
            // 游戏分类信息
            gameType: gameType,
            difficulty: difficulty,
            category: category,
            
            // 分数和排名信息
            score: score,
            weightedScore: this.calculateWeightedScore(score, difficulty),
            rank: null, // 将在排序时计算
            
            // 时间信息
            timestamp: Date.now(),
            submissionTime: new Date().toISOString(),
            
            // 状态信息
            status: this.entryStatus.ACTIVE,
            verified: false,
            
            // 扩展数据
            gameData: this.createGameDataStructure(gameType, metadata.gameData || {}),
            playerStats: this.createPlayerStatsStructure(metadata.playerStats || {}),
            
            // 元数据
            metadata: {
                version: this.version,
                submissionId: this.generateSubmissionId(),
                clientVersion: metadata.clientVersion || '1.0.0',
                deviceInfo: metadata.deviceInfo || null,
                antiCheatData: metadata.antiCheatData || null
            }
        };
        
        // 验证条目完整性
        this.validateEntry(entry);
        
        return entry;
    }
    
    /**
     * 创建游戏数据结构（根据游戏类型定制）
     * @param {string} gameType - 游戏类型
     * @param {Object} data - 原始游戏数据
     * @returns {Object} 标准化的游戏数据
     */
    createGameDataStructure(gameType, data) {
        const baseStructure = {
            duration: data.duration || 0,           // 游戏时长（毫秒）
            totalHits: data.totalHits || 0,         // 总击中次数
            missedHits: data.missedHits || 0,       // 错过次数
            accuracy: data.accuracy || 0,           // 准确率
            level: data.level || 1,                 // 关卡等级
            difficultyMultiplier: this.difficultyWeights[data.difficulty] || 1.0
        };
        
        // 根据游戏类型添加特定数据
        switch (gameType) {
            case this.gameTypes.SPARK_CATCHER:
                return {
                    ...baseStructure,
                    perfectHits: data.perfectHits || 0,     // 完美击中
                    combo: data.combo || 0,                 // 最高连击
                    sparksCollected: data.sparksCollected || 0, // 收集的光点数
                    powerUpsUsed: data.powerUpsUsed || 0    // 使用的道具数
                };
                
            case this.gameTypes.QUANTUM_RESONATOR:
                return {
                    ...baseStructure,
                    chainReactions: data.chainReactions || 0,   // 连锁反应次数
                    resonanceLevel: data.resonanceLevel || 0,   // 共鸣等级
                    particlesActivated: data.particlesActivated || 0, // 激活的粒子数
                    quantumEfficiency: data.quantumEfficiency || 0    // 量子效率
                };
                
            case this.gameTypes.TEMPORAL_WEAVER:
                return {
                    ...baseStructure,
                    timeManipulations: data.timeManipulations || 0, // 时间操控次数
                    timelineLength: data.timelineLength || 0,       // 时间线长度
                    temporalAccuracy: data.temporalAccuracy || 0,   // 时间精度
                    paradoxesResolved: data.paradoxesResolved || 0  // 解决的悖论数
                };
                
            default:
                return baseStructure;
        }
    }
    
    /**
     * 创建玩家统计数据结构
     * @param {Object} stats - 原始统计数据
     * @returns {Object} 标准化的玩家统计
     */
    createPlayerStatsStructure(stats) {
        return {
            totalGames: stats.totalGames || 0,          // 总游戏次数
            totalPlayTime: stats.totalPlayTime || 0,    // 总游戏时间
            averageScore: stats.averageScore || 0,      // 平均分数
            bestScore: stats.bestScore || 0,            // 最佳分数
            winRate: stats.winRate || 0,                // 胜率
            improvementRate: stats.improvementRate || 0, // 进步率
            consistencyScore: stats.consistencyScore || 0, // 一致性分数
            skillLevel: stats.skillLevel || 'beginner'   // 技能等级
        };
    }
    
    /**
     * 计算加权分数（考虑难度系数）
     * @param {number} score - 原始分数
     * @param {string} difficulty - 难度等级
     * @returns {number} 加权分数
     */
    calculateWeightedScore(score, difficulty) {
        const weight = this.difficultyWeights[difficulty] || 1.0;
        return Math.round(score * weight);
    }
    
    /**
     * 生成条目唯一ID
     * @param {string} playerId - 玩家ID
     * @param {string} gameType - 游戏类型
     * @param {string} difficulty - 难度等级
     * @param {string} category - 排行榜类别
     * @returns {string} 唯一ID
     */
    generateEntryId(playerId, gameType, difficulty, category) {
        const timestamp = Date.now();
        const hash = this.simpleHash(`${playerId}_${gameType}_${difficulty}_${category}_${timestamp}`);
        return `entry_${hash}`;
    }
    
    /**
     * 生成提交ID
     * @returns {string} 提交ID
     */
    generateSubmissionId() {
        return `sub_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    
    /**
     * 简单哈希函数
     * @param {string} str - 输入字符串
     * @returns {string} 哈希值
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }
    
    /**
     * 验证必需参数
     * @param {Object} params - 参数对象
     */
    validateRequiredParams(params) {
        const required = ['playerId', 'playerName', 'gameType', 'difficulty', 'category', 'score'];
        const missing = required.filter(param => !params[param] && params[param] !== 0);
        
        if (missing.length > 0) {
            throw new Error(`缺少必需参数: ${missing.join(', ')}`);
        }
        
        // 验证枚举值
        if (!Object.values(this.gameTypes).includes(params.gameType)) {
            throw new Error(`无效的游戏类型: ${params.gameType}`);
        }
        
        if (!Object.values(this.difficultyLevels).includes(params.difficulty)) {
            throw new Error(`无效的难度等级: ${params.difficulty}`);
        }
        
        if (!Object.values(this.leaderboardCategories).includes(params.category)) {
            throw new Error(`无效的排行榜类别: ${params.category}`);
        }
        
        // 验证分数
        if (typeof params.score !== 'number' || params.score < 0) {
            throw new Error(`无效的分数值: ${params.score}`);
        }
    }
    
    /**
     * 验证排行榜条目完整性
     * @param {Object} entry - 排行榜条目
     */
    validateEntry(entry) {
        // 验证基础结构
        const requiredFields = [
            'entryId', 'playerId', 'playerName', 'gameType', 
            'difficulty', 'category', 'score', 'timestamp'
        ];
        
        for (const field of requiredFields) {
            if (entry[field] === undefined || entry[field] === null) {
                throw new Error(`排行榜条目缺少必需字段: ${field}`);
            }
        }
        
        // 验证数据类型
        if (typeof entry.score !== 'number') {
            throw new Error('分数必须是数字类型');
        }
        
        if (typeof entry.timestamp !== 'number') {
            throw new Error('时间戳必须是数字类型');
        }
        
        console.log(`✅ 排行榜条目验证通过: ${entry.entryId}`);
    }
}

// 导出数据模型类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedLeaderboardDataModel;
} else if (typeof window !== 'undefined') {
    window.UnifiedLeaderboardDataModel = UnifiedLeaderboardDataModel;
}
