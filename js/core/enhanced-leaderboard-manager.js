/**
 * Split-Second Spark - 增强排行榜管理器
 * 基于现有排行榜系统，提供完整的增删改查接口和高级功能
 * 
 * 功能特性:
 * - 统一的CRUD操作接口
 * - 批量数据操作
 * - 高级条件查询
 * - 数据聚合和统计
 * - 实时排名计算
 * - 数据验证和完整性检查
 */

class EnhancedLeaderboardManager {
    constructor() {
        this.initialized = false;
        
        // 依赖的组件
        this.dataModel = null;
        this.indexManager = null;
        this.storageService = null;
        
        // 排行榜数据存储
        this.leaderboards = new Map();
        
        // 操作配置
        this.config = {
            maxBatchSize: 100,          // 批量操作最大数量
            maxQueryResults: 1000,      // 查询结果最大数量
            enableRealTimeRanking: true, // 启用实时排名
            enableDataValidation: true,  // 启用数据验证
            autoSave: true,             // 自动保存
            saveInterval: 30000         // 保存间隔（30秒）
        };
        
        // 操作统计
        this.stats = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            lastOperation: null,
            operationHistory: []
        };
        
        // 自动保存定时器
        this.autoSaveTimer = null;
        
        console.log('🚀 增强排行榜管理器已创建');
    }
    
    /**
     * 初始化管理器
     */
    async init() {
        try {
            // 初始化依赖组件
            await this.initializeDependencies();
            
            // 加载现有数据
            await this.loadExistingData();
            
            // 启动自动保存
            if (this.config.autoSave) {
                this.startAutoSave();
            }
            
            this.initialized = true;
            console.log('✅ 增强排行榜管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 增强排行榜管理器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化依赖组件
     */
    async initializeDependencies() {
        // 初始化数据模型
        if (!this.dataModel) {
            this.dataModel = new UnifiedLeaderboardDataModel();
        }
        
        // 初始化索引管理器
        if (!this.indexManager) {
            this.indexManager = new LeaderboardIndexManager();
            await this.indexManager.init();
        }
        
        // 获取存储服务
        if (!this.storageService && window.storageService) {
            this.storageService = window.storageService;
        }
        
        console.log('📦 依赖组件初始化完成');
    }
    
    /**
     * 创建排行榜条目
     * @param {Object} entryData - 条目数据
     * @returns {Promise<Object>} 创建的条目
     */
    async createEntry(entryData) {
        try {
            this.validateInitialized();
            
            // 使用统一数据模型创建条目
            const entry = this.dataModel.createLeaderboardEntry(entryData);
            
            // 添加到内存存储
            const leaderboardKey = this.getLeaderboardKey(entry);
            if (!this.leaderboards.has(leaderboardKey)) {
                this.leaderboards.set(leaderboardKey, []);
            }
            
            const leaderboard = this.leaderboards.get(leaderboardKey);
            leaderboard.push(entry);
            
            // 排序并更新排名
            this.sortAndUpdateRanks(leaderboard);
            
            // 添加到索引
            this.indexManager.addToIndex(entry);
            
            // 保存到持久化存储
            if (this.storageService) {
                await this.saveLeaderboard(leaderboardKey, leaderboard);
            }
            
            // 更新统计
            this.updateOperationStats('create', true);
            
            console.log(`✅ 排行榜条目已创建: ${entry.entryId}`);
            return entry;
            
        } catch (error) {
            this.updateOperationStats('create', false, error);
            console.error('❌ 创建排行榜条目失败:', error);
            throw error;
        }
    }
    
    /**
     * 批量创建排行榜条目
     * @param {Array} entriesData - 条目数据数组
     * @returns {Promise<Array>} 创建结果
     */
    async createBatch(entriesData) {
        try {
            this.validateInitialized();
            this.validateBatchSize(entriesData);
            
            const results = [];
            const errors = [];
            
            for (let i = 0; i < entriesData.length; i++) {
                try {
                    const entry = await this.createEntry(entriesData[i]);
                    results.push({ index: i, success: true, entry: entry });
                } catch (error) {
                    errors.push({ index: i, success: false, error: error.message });
                }
            }
            
            console.log(`📊 批量创建完成: ${results.length} 成功, ${errors.length} 失败`);
            
            return {
                successful: results,
                failed: errors,
                totalProcessed: entriesData.length
            };
            
        } catch (error) {
            console.error('❌ 批量创建失败:', error);
            throw error;
        }
    }
    
    /**
     * 查询排行榜条目
     * @param {Object} query - 查询条件
     * @returns {Promise<Array>} 查询结果
     */
    async query(query) {
        try {
            this.validateInitialized();
            
            // 使用索引管理器进行查询
            let results = this.indexManager.query(query);
            
            // 应用额外的过滤条件
            if (query.filters) {
                results = this.applyFilters(results, query.filters);
            }
            
            // 应用排序
            if (query.sort) {
                results = this.applySorting(results, query.sort);
            }
            
            // 应用分页
            if (query.pagination) {
                results = this.applyPagination(results, query.pagination);
            }
            
            // 限制结果数量
            const limit = Math.min(query.limit || this.config.maxQueryResults, this.config.maxQueryResults);
            results = results.slice(0, limit);
            
            this.updateOperationStats('query', true);
            
            console.log(`🔍 查询完成: 返回 ${results.length} 条结果`);
            return results;
            
        } catch (error) {
            this.updateOperationStats('query', false, error);
            console.error('❌ 查询失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新排行榜条目
     * @param {string} entryId - 条目ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新后的条目
     */
    async updateEntry(entryId, updateData) {
        try {
            this.validateInitialized();
            
            // 查找现有条目
            const existingEntry = await this.findEntryById(entryId);
            if (!existingEntry) {
                throw new Error(`条目不存在: ${entryId}`);
            }
            
            // 创建更新后的条目
            const updatedEntry = { ...existingEntry, ...updateData };
            updatedEntry.metadata.lastModified = Date.now();
            
            // 验证更新后的数据
            if (this.config.enableDataValidation) {
                this.dataModel.validateEntry(updatedEntry);
            }
            
            // 从旧位置移除
            await this.removeEntry(entryId, false); // 不保存，避免重复操作
            
            // 添加到新位置
            const leaderboardKey = this.getLeaderboardKey(updatedEntry);
            if (!this.leaderboards.has(leaderboardKey)) {
                this.leaderboards.set(leaderboardKey, []);
            }
            
            const leaderboard = this.leaderboards.get(leaderboardKey);
            leaderboard.push(updatedEntry);
            
            // 排序并更新排名
            this.sortAndUpdateRanks(leaderboard);
            
            // 更新索引
            this.indexManager.removeFromIndex(existingEntry);
            this.indexManager.addToIndex(updatedEntry);
            
            // 保存到持久化存储
            if (this.storageService) {
                await this.saveLeaderboard(leaderboardKey, leaderboard);
            }
            
            this.updateOperationStats('update', true);
            
            console.log(`✅ 排行榜条目已更新: ${entryId}`);
            return updatedEntry;
            
        } catch (error) {
            this.updateOperationStats('update', false, error);
            console.error('❌ 更新排行榜条目失败:', error);
            throw error;
        }
    }
    
    /**
     * 删除排行榜条目
     * @param {string} entryId - 条目ID
     * @param {boolean} save - 是否立即保存
     * @returns {Promise<boolean>} 删除结果
     */
    async removeEntry(entryId, save = true) {
        try {
            this.validateInitialized();
            
            // 查找并移除条目
            let removed = false;
            let removedEntry = null;
            
            for (const [leaderboardKey, leaderboard] of this.leaderboards.entries()) {
                const index = leaderboard.findIndex(entry => entry.entryId === entryId);
                if (index !== -1) {
                    removedEntry = leaderboard.splice(index, 1)[0];
                    removed = true;
                    
                    // 更新排名
                    this.updateRanks(leaderboard);
                    
                    // 保存到持久化存储
                    if (save && this.storageService) {
                        await this.saveLeaderboard(leaderboardKey, leaderboard);
                    }
                    
                    break;
                }
            }
            
            if (removed && removedEntry) {
                // 从索引中移除
                this.indexManager.removeFromIndex(removedEntry);
                
                this.updateOperationStats('delete', true);
                console.log(`✅ 排行榜条目已删除: ${entryId}`);
            } else {
                console.warn(`⚠️ 未找到要删除的条目: ${entryId}`);
            }
            
            return removed;
            
        } catch (error) {
            this.updateOperationStats('delete', false, error);
            console.error('❌ 删除排行榜条目失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取玩家排名信息
     * @param {string} playerId - 玩家ID
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 排名信息
     */
    async getPlayerRanking(playerId, options = {}) {
        try {
            this.validateInitialized();
            
            const query = {
                playerId: playerId,
                ...options
            };
            
            const playerEntries = await this.query(query);
            
            const ranking = {
                playerId: playerId,
                totalEntries: playerEntries.length,
                rankings: [],
                summary: {
                    bestScore: 0,
                    averageScore: 0,
                    totalGames: playerEntries.length,
                    favoriteGame: null,
                    favoriteDifficulty: null
                }
            };
            
            // 按游戏类型和难度分组计算排名
            const groupedEntries = this.groupEntriesByGameAndDifficulty(playerEntries);
            
            for (const [key, entries] of groupedEntries.entries()) {
                const [gameType, difficulty, category] = key.split('_');
                
                // 获取该分类的完整排行榜
                const fullLeaderboard = await this.query({
                    gameType: gameType,
                    difficulty: difficulty,
                    category: category,
                    limit: this.config.maxQueryResults
                });
                
                // 计算玩家在该分类中的排名
                const bestEntry = entries.reduce((best, current) => 
                    current.score > best.score ? current : best
                );
                
                const rank = fullLeaderboard.findIndex(entry => entry.entryId === bestEntry.entryId) + 1;
                
                ranking.rankings.push({
                    gameType: gameType,
                    difficulty: difficulty,
                    category: category,
                    rank: rank,
                    totalPlayers: fullLeaderboard.length,
                    bestScore: bestEntry.score,
                    percentile: rank > 0 ? ((fullLeaderboard.length - rank + 1) / fullLeaderboard.length * 100).toFixed(1) : 0
                });
            }
            
            // 计算汇总信息
            this.calculatePlayerSummary(ranking, playerEntries);
            
            console.log(`📊 玩家排名信息已生成: ${playerId}`);
            return ranking;
            
        } catch (error) {
            console.error('❌ 获取玩家排名失败:', error);
            throw error;
        }
    }
    
    /**
     * 数据聚合查询
     * @param {Object} aggregation - 聚合条件
     * @returns {Promise<Object>} 聚合结果
     */
    async aggregate(aggregation) {
        try {
            this.validateInitialized();
            
            const { groupBy, metrics, filters } = aggregation;
            
            // 获取基础数据
            let data = [];
            for (const leaderboard of this.leaderboards.values()) {
                data = data.concat(leaderboard);
            }
            
            // 应用过滤器
            if (filters) {
                data = this.applyFilters(data, filters);
            }
            
            // 按指定字段分组
            const grouped = this.groupData(data, groupBy);
            
            // 计算指标
            const results = {};
            for (const [groupKey, groupData] of grouped.entries()) {
                results[groupKey] = this.calculateMetrics(groupData, metrics);
            }
            
            console.log(`📈 数据聚合完成: ${Object.keys(results).length} 个分组`);
            return results;
            
        } catch (error) {
            console.error('❌ 数据聚合失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取排行榜统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const stats = {
            totalLeaderboards: this.leaderboards.size,
            totalEntries: 0,
            entriesByGame: {},
            entriesByDifficulty: {},
            operationStats: { ...this.stats },
            indexStats: this.indexManager ? this.indexManager.getStats() : null,
            memoryUsage: this.estimateMemoryUsage()
        };
        
        // 统计条目数量
        for (const leaderboard of this.leaderboards.values()) {
            stats.totalEntries += leaderboard.length;
            
            for (const entry of leaderboard) {
                // 按游戏类型统计
                if (!stats.entriesByGame[entry.gameType]) {
                    stats.entriesByGame[entry.gameType] = 0;
                }
                stats.entriesByGame[entry.gameType]++;
                
                // 按难度统计
                if (!stats.entriesByDifficulty[entry.difficulty]) {
                    stats.entriesByDifficulty[entry.difficulty] = 0;
                }
                stats.entriesByDifficulty[entry.difficulty]++;
            }
        }
        
        return stats;
    }
    
    /**
     * 获取排行榜键
     * @param {Object} entry - 排行榜条目
     * @returns {string} 排行榜键
     */
    getLeaderboardKey(entry) {
        return `${entry.gameType}_${entry.difficulty}_${entry.category}`;
    }
    
    /**
     * 排序并更新排名
     * @param {Array} leaderboard - 排行榜数组
     */
    sortAndUpdateRanks(leaderboard) {
        // 按分数降序排序
        leaderboard.sort((a, b) => b.score - a.score);
        
        // 更新排名
        this.updateRanks(leaderboard);
    }
    
    /**
     * 更新排名
     * @param {Array} leaderboard - 排行榜数组
     */
    updateRanks(leaderboard) {
        for (let i = 0; i < leaderboard.length; i++) {
            leaderboard[i].rank = i + 1;
        }
    }
    
    /**
     * 验证是否已初始化
     */
    validateInitialized() {
        if (!this.initialized) {
            throw new Error('增强排行榜管理器未初始化');
        }
    }
    
    /**
     * 验证批量操作大小
     * @param {Array} data - 数据数组
     */
    validateBatchSize(data) {
        if (!Array.isArray(data)) {
            throw new Error('批量数据必须是数组');
        }
        
        if (data.length > this.config.maxBatchSize) {
            throw new Error(`批量操作大小超过限制: ${data.length} > ${this.config.maxBatchSize}`);
        }
    }
    
    /**
     * 更新操作统计
     * @param {string} operation - 操作类型
     * @param {boolean} success - 是否成功
     * @param {Error} error - 错误信息
     */
    updateOperationStats(operation, success, error = null) {
        this.stats.totalOperations++;
        
        if (success) {
            this.stats.successfulOperations++;
        } else {
            this.stats.failedOperations++;
        }
        
        this.stats.lastOperation = {
            type: operation,
            success: success,
            timestamp: Date.now(),
            error: error ? error.message : null
        };
        
        // 保留最近100次操作历史
        this.stats.operationHistory.push(this.stats.lastOperation);
        if (this.stats.operationHistory.length > 100) {
            this.stats.operationHistory.shift();
        }
    }
    
    /**
     * 启动自动保存
     */
    startAutoSave() {
        this.autoSaveTimer = setInterval(async () => {
            try {
                await this.saveAllLeaderboards();
                console.log('💾 自动保存完成');
            } catch (error) {
                console.error('❌ 自动保存失败:', error);
            }
        }, this.config.saveInterval);
    }
    
    /**
     * 保存所有排行榜
     */
    async saveAllLeaderboards() {
        if (!this.storageService) return;
        
        const savePromises = [];
        for (const [key, leaderboard] of this.leaderboards.entries()) {
            savePromises.push(this.saveLeaderboard(key, leaderboard));
        }
        
        await Promise.all(savePromises);
    }
    
    /**
     * 保存单个排行榜
     * @param {string} key - 排行榜键
     * @param {Array} leaderboard - 排行榜数据
     */
    async saveLeaderboard(key, leaderboard) {
        if (!this.storageService) return;
        
        const storageKey = `enhanced_leaderboard.${key}`;
        await this.storageService.put(storageKey, {
            key: key,
            entries: leaderboard,
            lastUpdated: Date.now(),
            version: this.dataModel.version
        });
    }
    
    /**
     * 加载现有数据
     */
    async loadExistingData() {
        if (!this.storageService) return;
        
        try {
            const keys = await this.storageService.list('enhanced_leaderboard.');
            
            for (const key of keys) {
                const data = await this.storageService.get(key);
                if (data && data.entries) {
                    this.leaderboards.set(data.key, data.entries);
                    
                    // 添加到索引
                    for (const entry of data.entries) {
                        this.indexManager.addToIndex(entry);
                    }
                }
            }
            
            console.log(`📂 加载了 ${this.leaderboards.size} 个排行榜`);
            
        } catch (error) {
            console.error('❌ 加载现有数据失败:', error);
        }
    }
    
    /**
     * 估算内存使用量
     * @returns {number} 估算的内存使用量（字节）
     */
    estimateMemoryUsage() {
        let size = 0;
        
        // 估算排行榜数据大小
        for (const leaderboard of this.leaderboards.values()) {
            size += leaderboard.length * 500; // 每个条目约500字节
        }
        
        // 加上索引管理器的内存使用
        if (this.indexManager) {
            size += this.indexManager.estimateMemoryUsage();
        }
        
        return size;
    }
    
    /**
     * 根据ID查找条目
     * @param {string} entryId - 条目ID
     * @returns {Object|null} 找到的条目
     */
    async findEntryById(entryId) {
        for (const leaderboard of this.leaderboards.values()) {
            const entry = leaderboard.find(e => e.entryId === entryId);
            if (entry) return entry;
        }
        return null;
    }

    /**
     * 应用过滤器
     * @param {Array} data - 数据数组
     * @param {Object} filters - 过滤条件
     * @returns {Array} 过滤后的数据
     */
    applyFilters(data, filters) {
        return data.filter(entry => {
            for (const [key, value] of Object.entries(filters)) {
                if (entry[key] !== value) return false;
            }
            return true;
        });
    }

    /**
     * 应用排序
     * @param {Array} data - 数据数组
     * @param {Object} sort - 排序条件
     * @returns {Array} 排序后的数据
     */
    applySorting(data, sort) {
        const { field, order = 'desc' } = sort;

        return data.sort((a, b) => {
            const aValue = a[field];
            const bValue = b[field];

            if (order === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    /**
     * 应用分页
     * @param {Array} data - 数据数组
     * @param {Object} pagination - 分页参数
     * @returns {Array} 分页后的数据
     */
    applyPagination(data, pagination) {
        const { page = 1, pageSize = 20 } = pagination;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;

        return data.slice(start, end);
    }

    /**
     * 按游戏和难度分组条目
     * @param {Array} entries - 条目数组
     * @returns {Map} 分组后的条目
     */
    groupEntriesByGameAndDifficulty(entries) {
        const grouped = new Map();

        for (const entry of entries) {
            const key = `${entry.gameType}_${entry.difficulty}_${entry.category}`;
            if (!grouped.has(key)) {
                grouped.set(key, []);
            }
            grouped.get(key).push(entry);
        }

        return grouped;
    }

    /**
     * 计算玩家汇总信息
     * @param {Object} ranking - 排名对象
     * @param {Array} entries - 玩家条目
     */
    calculatePlayerSummary(ranking, entries) {
        if (entries.length === 0) return;

        const summary = ranking.summary;

        // 计算最佳分数和平均分数
        summary.bestScore = Math.max(...entries.map(e => e.score));
        summary.averageScore = Math.round(entries.reduce((sum, e) => sum + e.score, 0) / entries.length);

        // 统计最喜欢的游戏类型
        const gameTypeCounts = {};
        const difficultyCounts = {};

        for (const entry of entries) {
            gameTypeCounts[entry.gameType] = (gameTypeCounts[entry.gameType] || 0) + 1;
            difficultyCounts[entry.difficulty] = (difficultyCounts[entry.difficulty] || 0) + 1;
        }

        summary.favoriteGame = Object.keys(gameTypeCounts).reduce((a, b) =>
            gameTypeCounts[a] > gameTypeCounts[b] ? a : b
        );

        summary.favoriteDifficulty = Object.keys(difficultyCounts).reduce((a, b) =>
            difficultyCounts[a] > difficultyCounts[b] ? a : b
        );
    }

    /**
     * 按指定字段分组数据
     * @param {Array} data - 数据数组
     * @param {string|Array} groupBy - 分组字段
     * @returns {Map} 分组后的数据
     */
    groupData(data, groupBy) {
        const grouped = new Map();

        for (const item of data) {
            let key;
            if (Array.isArray(groupBy)) {
                key = groupBy.map(field => item[field]).join('_');
            } else {
                key = item[groupBy];
            }

            if (!grouped.has(key)) {
                grouped.set(key, []);
            }
            grouped.get(key).push(item);
        }

        return grouped;
    }

    /**
     * 计算指标
     * @param {Array} data - 数据数组
     * @param {Array} metrics - 指标列表
     * @returns {Object} 计算结果
     */
    calculateMetrics(data, metrics) {
        const results = {};

        for (const metric of metrics) {
            switch (metric) {
                case 'count':
                    results.count = data.length;
                    break;
                case 'sum':
                    results.sum = data.reduce((sum, item) => sum + item.score, 0);
                    break;
                case 'avg':
                    results.avg = data.length > 0 ?
                        Math.round(data.reduce((sum, item) => sum + item.score, 0) / data.length) : 0;
                    break;
                case 'max':
                    results.max = data.length > 0 ? Math.max(...data.map(item => item.score)) : 0;
                    break;
                case 'min':
                    results.min = data.length > 0 ? Math.min(...data.map(item => item.score)) : 0;
                    break;
            }
        }

        return results;
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }

        if (this.indexManager) {
            this.indexManager.destroy();
        }

        this.leaderboards.clear();
        this.initialized = false;

        console.log('🗑️ 增强排行榜管理器已销毁');
    }
}

// 导出增强排行榜管理器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedLeaderboardManager;
} else if (typeof window !== 'undefined') {
    window.EnhancedLeaderboardManager = EnhancedLeaderboardManager;
}
