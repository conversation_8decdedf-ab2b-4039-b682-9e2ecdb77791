/**
 * Split-Second Spark - 增强排行榜系统集成模块
 * 提供与现有游戏系统的无缝集成接口
 * 
 * 功能特性:
 * - 与现有排行榜系统兼容
 * - 自动数据迁移
 * - 渐进式升级支持
 * - 向后兼容性保证
 */

class EnhancedLeaderboardIntegration {
    constructor() {
        this.initialized = false;
        
        // 系统组件
        this.components = {
            dataModel: null,
            indexManager: null,
            leaderboardManager: null,
            realtimeUpdater: null,
            validator: null,
            analytics: null
        };
        
        // 兼容性配置
        this.compatibility = {
            enableLegacySupport: true,      // 启用旧版本支持
            autoMigration: true,            // 自动数据迁移
            fallbackToLegacy: true,         // 失败时回退到旧系统
            validateMigration: true         // 验证迁移数据
        };
        
        // 集成状态
        this.integrationStatus = {
            systemReady: false,
            migrationComplete: false,
            legacySystemActive: false,
            errorCount: 0,
            lastError: null
        };
        
        console.log('🔗 增强排行榜系统集成模块已创建');
    }
    
    /**
     * 初始化集成系统
     * @param {Object} options - 初始化选项
     */
    async initialize(options = {}) {
        try {
            console.log('🚀 开始初始化增强排行榜系统...');
            
            // 合并配置
            this.compatibility = { ...this.compatibility, ...options.compatibility };
            
            // 检查现有系统
            await this.checkExistingSystems();
            
            // 初始化核心组件
            await this.initializeCoreComponents();
            
            // 执行数据迁移
            if (this.compatibility.autoMigration) {
                await this.performDataMigration();
            }
            
            // 设置兼容性层
            await this.setupCompatibilityLayer();
            
            // 验证系统完整性
            await this.validateSystemIntegrity();
            
            this.initialized = true;
            this.integrationStatus.systemReady = true;
            
            console.log('✅ 增强排行榜系统初始化完成');
            
            return {
                success: true,
                status: this.integrationStatus,
                components: Object.keys(this.components).filter(key => this.components[key])
            };
            
        } catch (error) {
            console.error('❌ 增强排行榜系统初始化失败:', error);
            
            this.integrationStatus.errorCount++;
            this.integrationStatus.lastError = error.message;
            
            // 如果启用了回退机制，尝试使用旧系统
            if (this.compatibility.fallbackToLegacy) {
                await this.fallbackToLegacySystem();
            }
            
            throw error;
        }
    }
    
    /**
     * 检查现有系统
     */
    async checkExistingSystems() {
        console.log('🔍 检查现有排行榜系统...');
        
        // 检查旧版排行榜管理器
        if (window.leaderboardManager) {
            console.log('📋 发现现有排行榜管理器');
            this.integrationStatus.legacySystemActive = true;
        }
        
        // 检查难度分类排行榜管理器
        if (window.difficultyLeaderboardManager) {
            console.log('📊 发现难度分类排行榜管理器');
        }
        
        // 检查存储服务
        if (!window.storageService) {
            console.log('💾 初始化存储服务...');
            window.storageService = new StorageService();
            await window.storageService.init();
        }
    }
    
    /**
     * 初始化核心组件
     */
    async initializeCoreComponents() {
        console.log('🏗️ 初始化核心组件...');
        
        try {
            // 初始化数据模型
            this.components.dataModel = new UnifiedLeaderboardDataModel();
            console.log('✅ 统一数据模型已初始化');
            
            // 初始化索引管理器
            this.components.indexManager = new LeaderboardIndexManager();
            await this.components.indexManager.init();
            console.log('✅ 索引管理器已初始化');
            
            // 初始化增强排行榜管理器
            this.components.leaderboardManager = new EnhancedLeaderboardManager();
            await this.components.leaderboardManager.init();
            console.log('✅ 增强排行榜管理器已初始化');
            
            // 初始化实时更新器
            this.components.realtimeUpdater = new RealtimeLeaderboardUpdater();
            await this.components.realtimeUpdater.init({
                leaderboardManager: this.components.leaderboardManager
            });
            console.log('✅ 实时更新器已初始化');
            
            // 初始化数据验证器
            this.components.validator = new LeaderboardValidator();
            console.log('✅ 数据验证器已初始化');
            
            // 初始化数据分析器
            this.components.analytics = new LeaderboardAnalytics();
            await this.components.analytics.init({
                leaderboardManager: this.components.leaderboardManager
            });
            console.log('✅ 数据分析器已初始化');
            
        } catch (error) {
            console.error('❌ 核心组件初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 执行数据迁移
     */
    async performDataMigration() {
        console.log('📦 开始数据迁移...');
        
        try {
            let migratedCount = 0;
            
            // 迁移旧版排行榜数据
            if (window.leaderboardManager && window.leaderboardManager.leaderboards) {
                migratedCount += await this.migrateLegacyLeaderboards();
            }
            
            // 迁移难度分类排行榜数据
            if (window.difficultyLeaderboardManager && window.difficultyLeaderboardManager.difficultyLeaderboards) {
                migratedCount += await this.migrateDifficultyLeaderboards();
            }
            
            // 验证迁移结果
            if (this.compatibility.validateMigration) {
                await this.validateMigrationData();
            }
            
            this.integrationStatus.migrationComplete = true;
            console.log(`✅ 数据迁移完成，共迁移 ${migratedCount} 条记录`);
            
        } catch (error) {
            console.error('❌ 数据迁移失败:', error);
            throw error;
        }
    }
    
    /**
     * 迁移旧版排行榜数据
     */
    async migrateLegacyLeaderboards() {
        let count = 0;
        
        try {
            for (const [type, leaderboard] of window.leaderboardManager.leaderboards.entries()) {
                if (leaderboard.entries && leaderboard.entries.length > 0) {
                    for (const entry of leaderboard.entries) {
                        try {
                            // 转换为新格式
                            const newEntry = this.convertLegacyEntry(entry, type);
                            
                            // 创建新条目
                            await this.components.leaderboardManager.createEntry(newEntry);
                            count++;
                            
                        } catch (error) {
                            console.warn('⚠️ 迁移条目失败:', error.message);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('❌ 迁移旧版排行榜失败:', error);
        }
        
        return count;
    }
    
    /**
     * 迁移难度分类排行榜数据
     */
    async migrateDifficultyLeaderboards() {
        let count = 0;
        
        try {
            for (const [difficulty, difficultyData] of window.difficultyLeaderboardManager.difficultyLeaderboards.entries()) {
                for (const [category, leaderboard] of difficultyData.entries()) {
                    if (leaderboard.entries && leaderboard.entries.length > 0) {
                        for (const entry of leaderboard.entries) {
                            try {
                                // 转换为新格式
                                const newEntry = this.convertDifficultyEntry(entry, difficulty, category);
                                
                                // 创建新条目
                                await this.components.leaderboardManager.createEntry(newEntry);
                                count++;
                                
                            } catch (error) {
                                console.warn('⚠️ 迁移难度条目失败:', error.message);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('❌ 迁移难度排行榜失败:', error);
        }
        
        return count;
    }
    
    /**
     * 转换旧版条目格式
     */
    convertLegacyEntry(legacyEntry, type) {
        // 推断游戏类型和难度
        const gameType = this.inferGameType(legacyEntry);
        const difficulty = this.inferDifficulty(legacyEntry);
        const category = this.mapLegacyTypeToCategory(type);
        
        return {
            playerId: legacyEntry.playerId || `legacy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            playerName: legacyEntry.playerName || '未知玩家',
            gameType: gameType,
            difficulty: difficulty,
            category: category,
            score: legacyEntry.score || 0,
            metadata: {
                gameData: {
                    duration: legacyEntry.gameData?.duration || 0,
                    totalHits: legacyEntry.gameData?.totalHits || 0,
                    perfectHits: legacyEntry.perfectHits || 0,
                    combo: legacyEntry.combo || 0,
                    accuracy: legacyEntry.gameData?.accuracy || 0
                },
                legacy: true,
                originalType: type,
                migratedAt: Date.now()
            }
        };
    }
    
    /**
     * 转换难度条目格式
     */
    convertDifficultyEntry(entry, difficulty, category) {
        const gameType = this.inferGameType(entry);
        
        return {
            playerId: entry.playerId,
            playerName: entry.playerName,
            gameType: gameType,
            difficulty: difficulty,
            category: category,
            score: entry.score,
            metadata: {
                gameData: {
                    duration: entry.gameData?.duration || 0,
                    totalHits: entry.gameData?.totalHits || 0,
                    perfectHits: entry.perfectHits || 0,
                    combo: entry.combo || 0,
                    accuracy: entry.accuracy || 0,
                    difficultyMultiplier: entry.gameData?.difficultyMultiplier || 1.0
                },
                migrated: true,
                originalDifficulty: difficulty,
                originalCategory: category,
                migratedAt: Date.now()
            }
        };
    }
    
    /**
     * 推断游戏类型
     */
    inferGameType(entry) {
        // 根据条目特征推断游戏类型
        if (entry.perfectHits !== undefined || entry.combo !== undefined) {
            return 'spark-catcher';
        } else if (entry.chainReactions !== undefined) {
            return 'quantum-resonator';
        } else if (entry.timeManipulations !== undefined) {
            return 'temporal-weaver';
        }
        
        // 默认为瞬光捕手
        return 'spark-catcher';
    }
    
    /**
     * 推断难度等级
     */
    inferDifficulty(entry) {
        // 根据分数范围推断难度
        const score = entry.score || 0;
        
        if (score < 1000) return 'beginner';
        if (score < 5000) return 'easy';
        if (score < 15000) return 'normal';
        if (score < 30000) return 'hard';
        if (score < 50000) return 'expert';
        return 'master';
    }
    
    /**
     * 映射旧版类型到新类别
     */
    mapLegacyTypeToCategory(type) {
        const mapping = {
            'global_high_score': 'high_score',
            'official_global_high_score': 'high_score',
            'daily_high_score': 'daily_high_score',
            'official_daily_high_score': 'daily_high_score',
            'weekly_high_score': 'weekly_high_score',
            'official_weekly_high_score': 'weekly_high_score',
            'monthly_high_score': 'monthly_high_score',
            'official_monthly_high_score': 'monthly_high_score',
            'perfect_hits': 'perfect_hits',
            'official_perfect_hits': 'perfect_hits',
            'combo_record': 'combo_record',
            'official_combo_record': 'combo_record',
            'level_completion': 'level_completion',
            'official_level_completion': 'level_completion'
        };
        
        return mapping[type] || 'high_score';
    }
    
    /**
     * 设置兼容性层
     */
    async setupCompatibilityLayer() {
        console.log('🔧 设置兼容性层...');
        
        if (this.compatibility.enableLegacySupport) {
            // 保持旧接口的兼容性
            this.setupLegacyAPICompatibility();
            
            // 设置事件转发
            this.setupEventForwarding();
        }
    }
    
    /**
     * 设置旧API兼容性
     */
    setupLegacyAPICompatibility() {
        // 为旧版排行榜管理器提供兼容接口
        if (window.leaderboardManager) {
            const originalSubmitScore = window.leaderboardManager.submitScore;
            
            window.leaderboardManager.submitScore = async (scoreData) => {
                try {
                    // 尝试使用新系统
                    const convertedData = this.convertLegacyScoreData(scoreData);
                    return await this.components.realtimeUpdater.submitScore(convertedData);
                } catch (error) {
                    console.warn('⚠️ 新系统提交失败，回退到旧系统:', error);
                    // 回退到旧系统
                    return originalSubmitScore.call(window.leaderboardManager, scoreData);
                }
            };
        }
    }
    
    /**
     * 转换旧版分数数据
     */
    convertLegacyScoreData(legacyData) {
        return {
            playerId: legacyData.playerId,
            playerName: legacyData.playerName,
            gameType: this.inferGameType(legacyData),
            difficulty: this.inferDifficulty(legacyData),
            category: 'high_score',
            score: legacyData.score,
            metadata: {
                gameData: legacyData.gameData || {},
                legacy: true
            }
        };
    }
    
    /**
     * 设置事件转发
     */
    setupEventForwarding() {
        // 将新系统的事件转发到旧系统
        if (this.components.realtimeUpdater && window.leaderboardManager) {
            // 这里可以添加事件转发逻辑
        }
    }
    
    /**
     * 验证系统完整性
     */
    async validateSystemIntegrity() {
        console.log('🔍 验证系统完整性...');
        
        try {
            // 检查组件状态
            for (const [name, component] of Object.entries(this.components)) {
                if (!component) {
                    throw new Error(`组件 ${name} 未正确初始化`);
                }
            }
            
            // 测试基本功能
            await this.performBasicFunctionalityTest();
            
            console.log('✅ 系统完整性验证通过');
            
        } catch (error) {
            console.error('❌ 系统完整性验证失败:', error);
            throw error;
        }
    }
    
    /**
     * 执行基本功能测试
     */
    async performBasicFunctionalityTest() {
        // 创建测试条目
        const testEntry = {
            playerId: 'integration_test',
            playerName: '集成测试',
            gameType: 'spark-catcher',
            difficulty: 'normal',
            category: 'high_score',
            score: 1000,
            metadata: {
                gameData: {
                    duration: 60000,
                    totalHits: 50,
                    perfectHits: 40,
                    combo: 10
                },
                test: true
            }
        };
        
        // 测试创建
        const created = await this.components.leaderboardManager.createEntry(testEntry);
        
        // 测试查询
        const results = await this.components.leaderboardManager.query({
            playerId: 'integration_test',
            limit: 1
        });
        
        if (results.length === 0) {
            throw new Error('基本功能测试失败：无法查询到测试条目');
        }
        
        // 清理测试数据
        await this.components.leaderboardManager.removeEntry(created.entryId);
    }
    
    /**
     * 回退到旧系统
     */
    async fallbackToLegacySystem() {
        console.log('🔄 回退到旧排行榜系统...');
        
        try {
            // 确保旧系统可用
            if (window.leaderboardManager && !window.leaderboardManager.initialized) {
                await window.leaderboardManager.init();
            }
            
            if (window.difficultyLeaderboardManager && !window.difficultyLeaderboardManager.initialized) {
                await window.difficultyLeaderboardManager.init();
            }
            
            this.integrationStatus.legacySystemActive = true;
            console.log('✅ 已回退到旧系统');
            
        } catch (error) {
            console.error('❌ 回退到旧系统失败:', error);
        }
    }
    
    /**
     * 获取集成状态
     */
    getIntegrationStatus() {
        return {
            ...this.integrationStatus,
            components: Object.keys(this.components).reduce((status, key) => {
                status[key] = !!this.components[key];
                return status;
            }, {}),
            compatibility: this.compatibility
        };
    }
    
    /**
     * 获取系统统计
     */
    getSystemStats() {
        const stats = {};
        
        if (this.components.leaderboardManager) {
            stats.leaderboard = this.components.leaderboardManager.getStatistics();
        }
        
        if (this.components.realtimeUpdater) {
            stats.realtime = this.components.realtimeUpdater.getStats();
        }
        
        if (this.components.validator) {
            stats.validator = this.components.validator.getStats();
        }
        
        if (this.components.analytics) {
            stats.analytics = this.components.analytics.getStats();
        }
        
        return stats;
    }
    
    /**
     * 销毁集成系统
     */
    destroy() {
        console.log('🗑️ 销毁增强排行榜集成系统...');
        
        // 销毁所有组件
        for (const component of Object.values(this.components)) {
            if (component && typeof component.destroy === 'function') {
                component.destroy();
            }
        }
        
        // 清空组件引用
        this.components = {};
        this.initialized = false;
        this.integrationStatus.systemReady = false;
        
        console.log('✅ 集成系统已销毁');
    }
}

// 全局集成实例
let enhancedLeaderboardIntegration = null;

/**
 * 初始化增强排行榜系统
 * @param {Object} options - 初始化选项
 * @returns {Promise<Object>} 初始化结果
 */
async function initializeEnhancedLeaderboardSystem(options = {}) {
    if (!enhancedLeaderboardIntegration) {
        enhancedLeaderboardIntegration = new EnhancedLeaderboardIntegration();
    }
    
    return await enhancedLeaderboardIntegration.initialize(options);
}

/**
 * 获取增强排行榜系统实例
 * @returns {EnhancedLeaderboardIntegration|null} 系统实例
 */
function getEnhancedLeaderboardSystem() {
    return enhancedLeaderboardIntegration;
}

// 导出集成类和函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        EnhancedLeaderboardIntegration,
        initializeEnhancedLeaderboardSystem,
        getEnhancedLeaderboardSystem
    };
} else if (typeof window !== 'undefined') {
    window.EnhancedLeaderboardIntegration = EnhancedLeaderboardIntegration;
    window.initializeEnhancedLeaderboardSystem = initializeEnhancedLeaderboardSystem;
    window.getEnhancedLeaderboardSystem = getEnhancedLeaderboardSystem;
}
