/**
 * Split-Second Spark - 统一难度配置系统
 * 支持多维度、多游戏的难度调整机制
 */

/**
 * 难度配置管理器
 * 提供统一的难度设置接口，支持所有游戏类型
 */
class DifficultyConfigManager {
    constructor() {
        this.initialized = false;
        
        // 难度等级定义
        this.difficultyLevels = {
            'beginner': {
                name: '新手',
                description: '适合初次接触游戏的玩家',
                multiplier: 0.6,
                color: '#4CAF50'
            },
            'easy': {
                name: '简单',
                description: '轻松愉快的游戏体验',
                multiplier: 0.8,
                color: '#8BC34A'
            },
            'normal': {
                name: '普通',
                description: '标准的游戏难度',
                multiplier: 1.0,
                color: '#FFC107'
            },
            'hard': {
                name: '困难',
                description: '具有挑战性的游戏体验',
                multiplier: 1.3,
                color: '#FF9800'
            },
            'expert': {
                name: '专家',
                description: '极具挑战性，适合高手',
                multiplier: 1.6,
                color: '#F44336'
            },
            'master': {
                name: '大师',
                description: '终极挑战，考验极限',
                multiplier: 2.0,
                color: '#9C27B0'
            }
        };

        // 当前难度设置
        this.currentDifficulty = 'normal';
        
        // 自定义难度设置
        this.customSettings = {};
        
        console.log('🎯 统一难度配置系统已创建');
    }

    /**
     * 初始化难度配置系统
     */
    async init() {
        try {
            // 从存储中加载用户的难度偏好
            if (window.storageService) {
                const savedDifficulty = await storageService.get('settings.difficulty', 'normal');
                const customSettings = await storageService.get('settings.customDifficulty', {});
                
                this.currentDifficulty = savedDifficulty;
                this.customSettings = customSettings;
            }
            
            this.initialized = true;
            console.log('✅ 难度配置系统初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 难度配置系统初始化失败:', error);
            this.initialized = true; // 即使失败也标记为已初始化，使用默认设置
            return false;
        }
    }

    /**
     * 获取指定游戏的完整难度配置
     * @param {string} gameType - 游戏类型 ('spark-catcher', 'quantum-resonator', 'temporal-weaver')
     * @param {string} difficulty - 难度等级
     * @returns {Object} 完整的难度配置对象
     */
    getGameDifficultyConfig(gameType, difficulty = null) {
        const targetDifficulty = difficulty || this.currentDifficulty;
        const difficultyInfo = this.difficultyLevels[targetDifficulty];
        
        if (!difficultyInfo) {
            console.warn(`⚠️ 未知难度等级: ${targetDifficulty}，使用普通难度`);
            return this.getGameDifficultyConfig(gameType, 'normal');
        }

        // 基础配置
        const baseConfig = {
            level: targetDifficulty,
            name: difficultyInfo.name,
            description: difficultyInfo.description,
            multiplier: difficultyInfo.multiplier,
            color: difficultyInfo.color
        };

        // 根据游戏类型返回特定配置
        switch (gameType) {
            case 'spark-catcher':
                return this.getSparkCatcherConfig(baseConfig);
            case 'quantum-resonator':
                return this.getQuantumResonatorConfig(baseConfig);
            case 'temporal-weaver':
                return this.getTemporalWeaverConfig(baseConfig);
            default:
                console.warn(`⚠️ 未知游戏类型: ${gameType}`);
                return baseConfig;
        }
    }

    /**
     * 瞬光捕手游戏难度配置
     * @param {Object} baseConfig - 基础配置
     * @returns {Object} 瞬光捕手专用难度配置
     */
    getSparkCatcherConfig(baseConfig) {
        const multiplier = baseConfig.multiplier;
        
        return {
            ...baseConfig,
            
            // 核心游戏机制
            gameplay: {
                // 玩家属性
                player: {
                    lives: Math.max(1, Math.floor(5 - multiplier * 2)), // 5→3→1 生命值
                    scoreMultiplier: multiplier, // 分数倍数
                    comboDecayRate: 0.95 + (multiplier - 1) * 0.02, // 连击衰减率
                    invulnerabilityTime: Math.max(500, 1500 - multiplier * 500) // 无敌时间
                },
                
                // 光点属性
                sparks: {
                    spawnRate: Math.max(500, 2000 - multiplier * 800), // 生成间隔
                    speed: 0.5 + multiplier * 0.8, // 移动速度
                    maxCount: Math.min(8, Math.floor(1 + multiplier * 3)), // 最大数量
                    lifespan: Math.max(2000, 5000 - multiplier * 1500), // 存活时间
                    size: Math.max(0.6, 1.2 - multiplier * 0.3) // 大小倍数
                },
                
                // 时机窗口
                timing: {
                    perfectWindow: Math.max(30, 120 - multiplier * 45), // 完美窗口
                    goodWindow: Math.max(60, 250 - multiplier * 95), // 良好窗口
                    comboWindow: Math.max(800, 1500 - multiplier * 350) // 连击窗口
                }
            },
            
            // 关卡设计
            level: {
                duration: 30000, // 基础时长保持不变
                targetScore: Math.floor(1000 * multiplier), // 目标分数
                bonusThreshold: Math.floor(1500 * multiplier), // 奖励阈值
                timeBonus: multiplier > 1 ? true : false, // 时间奖励
                difficultyProgression: multiplier * 0.1 // 难度递增率
            },
            
            // 用户体验
            userExperience: {
                showHints: multiplier < 1.2, // 显示提示
                autoSave: true, // 自动保存
                retryLimit: multiplier > 1.5 ? 3 : -1, // 重试限制
                pauseOnFocusLoss: multiplier < 1.0, // 失焦暂停
                visualFeedback: {
                    particleEffects: true,
                    screenShake: multiplier > 1.0,
                    slowMotion: multiplier < 0.9 // 慢动作效果
                }
            }
        };
    }

    /**
     * 量子共鸣者游戏难度配置
     * @param {Object} baseConfig - 基础配置
     * @returns {Object} 量子共鸣者专用难度配置
     */
    getQuantumResonatorConfig(baseConfig) {
        const multiplier = baseConfig.multiplier;
        
        return {
            ...baseConfig,
            
            // 核心游戏机制
            gameplay: {
                // 量子物理参数
                quantum: {
                    fieldStrength: 0.8 + multiplier * 0.4, // 量子场强度
                    resonanceThreshold: Math.max(0.1, 0.4 - multiplier * 0.15), // 共鸣阈值
                    chainDecayRate: Math.max(0.85, 0.98 - multiplier * 0.06), // 链式衰减
                    waveSpeed: 150 + multiplier * 100, // 能量波速度
                    maxChainLength: Math.floor(8 + multiplier * 4) // 最大链长度
                },
                
                // 粒子属性
                particles: {
                    count: Math.floor(20 + multiplier * 15), // 粒子数量
                    energyRange: [30 - multiplier * 10, 50 + multiplier * 20], // 能量范围
                    frequencyRange: [220, 1760 + multiplier * 500], // 频率范围
                    connectionStrength: 0.2 + multiplier * 0.3 // 连接强度
                },
                
                // 物理模拟
                physics: {
                    gravity: { x: 0, y: multiplier * 50 }, // 重力影响
                    damping: Math.max(0.92, 0.99 - multiplier * 0.035), // 阻尼系数
                    timeScale: 1.0 + (multiplier - 1) * 0.2 // 时间缩放
                }
            },
            
            // 关卡设计
            level: {
                timeLimit: Math.max(60, 180 - multiplier * 60), // 时间限制
                targetScore: Math.floor(2000 * multiplier), // 目标分数
                maxMoves: multiplier > 1.3 ? Math.floor(25 - multiplier * 5) : -1, // 移动限制
                objectives: this.generateQuantumObjectives(multiplier) // 动态目标
            },
            
            // 用户体验
            userExperience: {
                showResonanceLines: multiplier < 1.1, // 显示共鸣线
                particleTrails: true, // 粒子轨迹
                audioFeedback: {
                    enabled: true,
                    complexity: Math.floor(1 + multiplier * 2) // 音频复杂度
                },
                tutorialMode: multiplier < 0.9 // 教程模式
            }
        };
    }

    /**
     * 时空织梦者游戏难度配置
     * @param {Object} baseConfig - 基础配置
     * @returns {Object} 时空织梦者专用难度配置
     */
    getTemporalWeaverConfig(baseConfig) {
        const multiplier = baseConfig.multiplier;
        
        return {
            ...baseConfig,
            
            // 核心游戏机制
            gameplay: {
                // 时间操控参数
                timeControl: {
                    maxRewindSteps: Math.max(50, Math.floor(150 - multiplier * 50)), // 最大回退步数
                    rewindSpeed: Math.max(1.5, 3.0 - multiplier * 0.75), // 倒流速度
                    fastForwardSpeed: 2.0 + multiplier * 1.0, // 快进速度
                    pauseDuration: Math.max(3000, 8000 - multiplier * 2500), // 暂停持续时间
                    timelineLength: Math.max(500, 1200 - multiplier * 350) // 时间线长度
                },
                
                // 梦境编织参数
                dreamWeaving: {
                    maxConnections: Math.floor(15 + multiplier * 10), // 最大连接数
                    weavingSpeed: 0.8 + multiplier * 0.4, // 编织速度
                    stabilityDecay: 0.98 - multiplier * 0.02, // 稳定性衰减
                    complexityFactor: multiplier // 复杂度因子
                },
                
                // 因果系统
                causality: {
                    maxParadoxes: Math.max(1, Math.floor(4 - multiplier)), // 最大悖论数
                    paradoxPenalty: multiplier * 0.2, // 悖论惩罚
                    causalityStrength: 1.0 + multiplier * 0.3 // 因果强度
                }
            },
            
            // 关卡设计
            level: {
                puzzleComplexity: Math.floor(3 + multiplier * 4), // 谜题复杂度
                timeLimit: multiplier > 1.2 ? Math.floor(300 - multiplier * 60) : -1, // 时间限制
                minMoves: Math.floor(5 + multiplier * 3), // 最少移动数
                dreamFragments: Math.floor(8 + multiplier * 6) // 梦境碎片数
            },
            
            // 用户体验
            userExperience: {
                showTimelinePreview: multiplier < 1.1, // 显示时间线预览
                autoHints: multiplier < 0.9, // 自动提示
                undoLimit: multiplier > 1.4 ? Math.floor(10 - multiplier * 2) : -1, // 撤销限制
                visualComplexity: Math.floor(1 + multiplier * 2) // 视觉复杂度
            }
        };
    }

    /**
     * 生成量子共鸣者的动态目标
     * @param {number} multiplier - 难度倍数
     * @returns {Array} 目标数组
     */
    generateQuantumObjectives(multiplier) {
        const objectives = [
            { type: 'score', target: Math.floor(2000 * multiplier), description: `达到${Math.floor(2000 * multiplier)}分` }
        ];

        if (multiplier >= 1.0) {
            objectives.push({
                type: 'chain_reaction',
                target: Math.floor(2 + multiplier),
                description: `创造${Math.floor(2 + multiplier)}次连锁反应`
            });
        }

        if (multiplier >= 1.3) {
            objectives.push({
                type: 'time_bonus',
                target: Math.floor(120 - multiplier * 30),
                description: `在${Math.floor(120 - multiplier * 30)}秒内完成`
            });
        }

        return objectives;
    }

    /**
     * 设置当前难度等级
     * @param {string} difficulty - 难度等级
     * @returns {Promise<boolean>} 设置是否成功
     */
    async setDifficulty(difficulty) {
        if (!this.difficultyLevels[difficulty]) {
            console.error(`❌ 无效的难度等级: ${difficulty}`);
            return false;
        }

        const oldDifficulty = this.currentDifficulty;
        this.currentDifficulty = difficulty;

        try {
            // 保存到存储
            if (window.storageService) {
                await window.storageService.set('settings.difficulty', difficulty);
            }

            console.log(`🎯 难度已更改: ${oldDifficulty} → ${difficulty}`);

            // 触发难度变更事件
            this.dispatchDifficultyChangeEvent(oldDifficulty, difficulty);

            return true;
        } catch (error) {
            console.error('❌ 保存难度设置失败:', error);
            this.currentDifficulty = oldDifficulty; // 回滚
            return false;
        }
    }

    /**
     * 获取当前难度等级
     * @returns {string} 当前难度等级
     */
    getCurrentDifficulty() {
        return this.currentDifficulty;
    }

    /**
     * 获取当前难度信息
     * @returns {Object} 难度信息对象
     */
    getCurrentDifficultyInfo() {
        return this.difficultyLevels[this.currentDifficulty];
    }

    /**
     * 获取所有可用的难度等级
     * @returns {Array} 难度等级数组
     */
    getAvailableDifficulties() {
        return Object.keys(this.difficultyLevels).map(key => ({
            key,
            ...this.difficultyLevels[key]
        }));
    }

    /**
     * 创建自定义难度配置
     * @param {string} name - 自定义难度名称
     * @param {Object} config - 自定义配置
     * @returns {Promise<boolean>} 创建是否成功
     */
    async createCustomDifficulty(name, config) {
        try {
            const customKey = `custom_${name.toLowerCase().replace(/\s+/g, '_')}`;

            this.customSettings[customKey] = {
                name,
                description: config.description || '自定义难度配置',
                isCustom: true,
                createdAt: Date.now(),
                config
            };

            // 保存到存储
            if (window.storageService) {
                await storageService.set('settings.customDifficulty', this.customSettings);
            }

            console.log(`✅ 自定义难度已创建: ${name}`);
            return true;
        } catch (error) {
            console.error('❌ 创建自定义难度失败:', error);
            return false;
        }
    }

    /**
     * 应用自定义难度配置
     * @param {string} gameType - 游戏类型
     * @param {Object} customConfig - 自定义配置
     * @returns {Object} 应用后的完整配置
     */
    applyCustomDifficultyConfig(gameType, customConfig) {
        // 获取基础配置
        const baseConfig = this.getGameDifficultyConfig(gameType, 'normal');

        // 深度合并自定义配置
        return this.deepMerge(baseConfig, customConfig);
    }

    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     */
    deepMerge(target, source) {
        const result = { ...target };

        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }

        return result;
    }

    /**
     * 计算难度评分
     * @param {string} gameType - 游戏类型
     * @param {string} difficulty - 难度等级
     * @returns {number} 难度评分 (0-100)
     */
    calculateDifficultyScore(gameType, difficulty = null) {
        const config = this.getGameDifficultyConfig(gameType, difficulty);
        const multiplier = config.multiplier;

        // 基础评分
        let score = multiplier * 50; // 50分为普通难度基准

        // 根据游戏类型调整评分
        switch (gameType) {
            case 'spark-catcher':
                // 考虑时机窗口、生命值等因素
                score += (120 - config.gameplay.timing.perfectWindow) * 0.2;
                score += (5 - config.gameplay.player.lives) * 5;
                break;

            case 'quantum-resonator':
                // 考虑共鸣阈值、时间限制等因素
                score += (0.4 - config.gameplay.quantum.resonanceThreshold) * 100;
                score += (180 - config.level.timeLimit) * 0.1;
                break;

            case 'temporal-weaver':
                // 考虑时间操控限制、谜题复杂度等因素
                score += (150 - config.gameplay.timeControl.maxRewindSteps) * 0.2;
                score += config.level.puzzleComplexity * 3;
                break;
        }

        return Math.max(0, Math.min(100, Math.round(score)));
    }

    /**
     * 获取难度建议
     * @param {Object} playerStats - 玩家统计数据
     * @param {string} gameType - 游戏类型
     * @returns {string} 建议的难度等级
     */
    getDifficultyRecommendation(playerStats, gameType) {
        if (!playerStats || !playerStats.totalGames) {
            return 'beginner'; // 新玩家推荐新手难度
        }

        const { totalGames, bestScore, averageScore, totalPlayTime } = playerStats;

        // 计算玩家熟练度
        let proficiencyScore = 0;

        // 游戏次数因子 (0-20分)
        proficiencyScore += Math.min(20, totalGames * 2);

        // 分数表现因子 (0-30分)
        if (averageScore && bestScore) {
            const scoreRatio = averageScore / bestScore;
            proficiencyScore += scoreRatio * 30;
        }

        // 游戏时长因子 (0-20分)
        const hoursPlayed = totalPlayTime / (1000 * 60 * 60);
        proficiencyScore += Math.min(20, hoursPlayed * 4);

        // 根据熟练度推荐难度
        if (proficiencyScore < 15) return 'beginner';
        if (proficiencyScore < 30) return 'easy';
        if (proficiencyScore < 50) return 'normal';
        if (proficiencyScore < 70) return 'hard';
        if (proficiencyScore < 85) return 'expert';
        return 'master';
    }

    /**
     * 触发难度变更事件
     * @param {string} oldDifficulty - 旧难度
     * @param {string} newDifficulty - 新难度
     */
    dispatchDifficultyChangeEvent(oldDifficulty, newDifficulty) {
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('difficultyChanged', {
                detail: {
                    oldDifficulty,
                    newDifficulty,
                    difficultyInfo: this.difficultyLevels[newDifficulty]
                }
            });
            window.dispatchEvent(event);
        }
    }

    /**
     * 重置为默认难度
     * @returns {Promise<boolean>} 重置是否成功
     */
    async resetToDefault() {
        return await this.setDifficulty('normal');
    }

    /**
     * 获取难度配置的JSON表示
     * @param {string} gameType - 游戏类型
     * @param {string} difficulty - 难度等级
     * @returns {string} JSON字符串
     */
    exportDifficultyConfig(gameType, difficulty = null) {
        const config = this.getGameDifficultyConfig(gameType, difficulty);
        return JSON.stringify(config, null, 2);
    }

    /**
     * 从JSON导入难度配置
     * @param {string} jsonConfig - JSON配置字符串
     * @returns {Object|null} 解析后的配置对象
     */
    importDifficultyConfig(jsonConfig) {
        try {
            return JSON.parse(jsonConfig);
        } catch (error) {
            console.error('❌ 导入难度配置失败:', error);
            return null;
        }
    }
}

// 创建全局难度配置管理器实例
const difficultyConfigManager = new DifficultyConfigManager();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.DifficultyConfigManager = DifficultyConfigManager;
    window.difficultyConfigManager = difficultyConfigManager;
}

// 支持CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DifficultyConfigManager,
        difficultyConfigManager
    };
}
